"""
当事方信息提取测试脚本
验证修复后的甲方乙方提取功能
"""

import sys
import re
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from document_processor import DocumentProcessor
from models import ElementCategory

def test_party_extraction():
    """测试当事方信息提取"""
    print("🧪 测试当事方信息提取修复效果")
    
    # 创建文档处理器
    processor = DocumentProcessor()
    
    # 测试用例1：标准格式
    test_case_1 = """
    技术开发合同
    
    甲方：北京科技发展有限公司
    地址：北京市海淀区中关村大街1号
    联系人：张三
    
    乙方：上海软件开发股份有限公司  
    地址：上海市浦东新区陆家嘴路100号
    联系人：李四
    
    双方经过平等协商，达成如下协议...
    """
    
    print("\n=== 测试用例1：标准格式 ===")
    elements_1 = processor._extract_parties(test_case_1)
    for element in elements_1:
        print(f"✅ {element.element_name}: {element.value}")
        print(f"   置信度: {element.confidence:.2f}")
    
    # 测试用例2：问题格式（之前出错的情况）
    test_case_2 = """
    甲方：1保密内容（包括技术信息和经营信息）：项目中所有标有保密的技术及商务文件。
    乙方：1保密内容（包括技术信息和经营信息）：项目中所有标有保密的技术及商务文件。
    """
    
    print("\n=== 测试用例2：问题格式（应该被过滤） ===")
    elements_2 = processor._extract_parties(test_case_2)
    if elements_2:
        for element in elements_2:
            print(f"❌ 错误提取: {element.element_name}: {element.value}")
    else:
        print("✅ 正确过滤了无效内容")
    
    # 测试用例3：复杂格式
    test_case_3 = """
    委托方：中国科学院计算技术研究所（以下简称甲方）
    住所：北京市海淀区科学院南路6号
    
    受托方：华为技术有限公司（以下简称乙方）
    住所：广东省深圳市龙岗区坂田华为基地
    
    根据《中华人民共和国合同法》...
    """
    
    print("\n=== 测试用例3：复杂格式 ===")
    elements_3 = processor._extract_parties(test_case_3)
    for element in elements_3:
        print(f"✅ {element.element_name}: {element.value}")
        print(f"   置信度: {element.confidence:.2f}")
    
    # 测试用例4：个人合同
    test_case_4 = """
    甲方：张伟
    身份证号：110101199001011234
    住址：北京市朝阳区建国路88号
    
    乙方：李明
    身份证号：310101199002022345  
    住址：上海市黄浦区南京路100号
    """
    
    print("\n=== 测试用例4：个人合同 ===")
    elements_4 = processor._extract_parties(test_case_4)
    for element in elements_4:
        print(f"✅ {element.element_name}: {element.value}")
        print(f"   置信度: {element.confidence:.2f}")

def test_validation_logic():
    """测试验证逻辑"""
    print("\n🔍 测试内容验证逻辑")
    
    processor = DocumentProcessor()
    
    # 测试有效内容
    valid_cases = [
        "北京科技发展有限公司",
        "上海软件开发股份有限公司",
        "张三",
        "李四",
        "中国科学院计算技术研究所"
    ]
    
    print("\n--- 有效内容测试 ---")
    for case in valid_cases:
        is_valid = processor._is_valid_party_info(case)
        print(f"{'✅' if is_valid else '❌'} {case}: {is_valid}")
    
    # 测试无效内容
    invalid_cases = [
        "1保密内容（包括技术信息和经营信息）：项目中所有标有保密的技术及商务文件",
        "应当保证其交付给甲方的研究开发成果不侵犯任何第三人的合法权益",
        "双方经过平等协商，在真实、充分地表达各自意愿的基础上",
        "技术风险存在并有可能致使研究开发失败",
        "保密",
        "a",
        ""
    ]
    
    print("\n--- 无效内容测试 ---")
    for case in invalid_cases:
        is_valid = processor._is_valid_party_info(case)
        print(f"{'❌' if not is_valid else '⚠️ '} {case[:50]}{'...' if len(case) > 50 else ''}: {is_valid}")

def test_cleaning_logic():
    """测试清理逻辑"""
    print("\n🧹 测试内容清理逻辑")
    
    processor = DocumentProcessor()
    
    test_cases = [
        ("北京科技发展有限公司（以下简称甲方）", "北京科技发展有限公司"),
        ("上海软件开发股份有限公司，以下简称乙方。", "上海软件开发股份有限公司"),
        ("中国科学院计算技术研究所    ", "中国科学院计算技术研究所"),
        ("华为技术有限公司；负责项目开发", "华为技术有限公司")
    ]
    
    for original, expected in test_cases:
        cleaned = processor._clean_party_info(original)
        success = cleaned == expected
        print(f"{'✅' if success else '❌'} 原文: {original}")
        print(f"   清理后: {cleaned}")
        print(f"   期望: {expected}")
        print()

if __name__ == "__main__":
    test_party_extraction()
    test_validation_logic() 
    test_cleaning_logic()
    print("\n🎯 当事方提取测试完成")
