"""
数据模型定义
定义系统中使用的所有数据结构和响应模型
"""

from pydantic import BaseModel, Field
from typing import List, Dict, Optional, Any
from datetime import datetime
from enum import Enum

class ContractType(str, Enum):
    """合同类型枚举"""
    SALES = "sales"           # 销售合同
    SERVICE = "service"       # 服务合同
    LEASE = "lease"          # 租赁合同
    EMPLOYMENT = "employment" # 劳动合同
    GENERAL = "general"      # 通用合同

class RiskLevel(str, Enum):
    """风险等级枚举"""
    HIGH = "high"     # 高风险
    MEDIUM = "medium" # 中风险
    LOW = "low"       # 低风险

class ElementCategory(str, Enum):
    """要素分类枚举"""
    DOCUMENT_STRUCTURE = "document_structure"  # 文档结构要素
    PARTY_INFO = "party_info"                 # 当事方信息
    CONTRACT_TERMS = "contract_terms"         # 合同条款
    FINANCIAL_TERMS = "financial_terms"      # 财务条款
    TIME_TERMS = "time_terms"                # 时间条款

class DocumentInfo(BaseModel):
    """文档基础信息"""
    filename: str = Field(..., description="文件名")
    file_size: int = Field(..., description="文件大小(字节)")
    total_paragraphs: int = Field(..., description="总段落数")
    total_tables: int = Field(0, description="总表格数")
    total_words: int = Field(0, description="总字数")
    created_time: Optional[datetime] = Field(None, description="文档创建时间")
    modified_time: Optional[datetime] = Field(None, description="文档修改时间")
    author: Optional[str] = Field(None, description="文档作者")

class ExtractedElement(BaseModel):
    """提取的要素信息"""
    element_name: str = Field(..., description="要素名称")
    element_category: ElementCategory = Field(..., description="要素分类")
    value: str = Field(..., description="提取的值")
    confidence: float = Field(..., ge=0.0, le=1.0, description="置信度")
    source_position: Dict[str, Any] = Field(..., description="在原文档中的位置")
    pattern_type: Optional[str] = Field(None, description="匹配的模式类型")

class MissingClause(BaseModel):
    """缺失条款信息"""
    clause_name: str = Field(..., description="条款名称")
    clause_type: str = Field(..., description="条款类型")
    importance_level: RiskLevel = Field(..., description="重要性等级")
    description: str = Field(..., description="条款描述")
    recommendation: str = Field(..., description="建议补充内容")

class RiskPoint(BaseModel):
    """风险点信息"""
    risk_id: str = Field(..., description="风险点ID")
    risk_type: str = Field(..., description="风险类型")
    risk_level: RiskLevel = Field(..., description="风险等级")
    description: str = Field(..., description="风险描述")
    location: str = Field(..., description="风险位置")
    suggestion: str = Field(..., description="处理建议")
    related_clause: Optional[str] = Field(None, description="相关条款")

class ContractAnalysisResult(BaseModel):
    """合同分析结果"""
    # 基础信息
    document_info: DocumentInfo = Field(..., description="文档信息")
    contract_type: ContractType = Field(..., description="合同类型")
    analysis_time: datetime = Field(default_factory=datetime.now, description="分析时间")
    processing_duration: float = Field(..., description="处理耗时(秒)")
    
    # 要素提取结果
    extracted_elements: List[ExtractedElement] = Field(default=[], description="提取的要素")
    elements_by_category: Dict[str, List[ExtractedElement]] = Field(default={}, description="按分类组织的要素")
    
    # 缺失条款检测
    missing_clauses: List[MissingClause] = Field(default=[], description="缺失的条款")
    
    # 风险识别
    risk_points: List[RiskPoint] = Field(default=[], description="识别的风险点")
    
    # 统计信息
    statistics: Dict[str, Any] = Field(default={}, description="统计信息")
    
    # 质量评估
    overall_confidence: float = Field(..., ge=0.0, le=1.0, description="整体置信度")
    completeness_score: float = Field(..., ge=0.0, le=1.0, description="完整性评分")
    risk_score: float = Field(..., ge=0.0, le=1.0, description="风险评分")

class AnalysisConfig(BaseModel):
    """分析配置"""
    enable_ai_analysis: bool = Field(True, description="启用AI分析")
    ai_api_timeout: int = Field(30, description="AI API超时时间(秒)")
    max_retries: int = Field(3, description="最大重试次数")
    confidence_threshold: float = Field(0.7, description="置信度阈值")
    
class HealthStatus(BaseModel):
    """健康状态"""
    status: str = Field(..., description="状态")
    timestamp: float = Field(..., description="时间戳")
    details: Optional[Dict[str, Any]] = Field(None, description="详细信息")

class ErrorResponse(BaseModel):
    """错误响应"""
    error: str = Field(..., description="错误信息")
    error_code: str = Field(..., description="错误代码")
    timestamp: datetime = Field(default_factory=datetime.now, description="错误时间")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情")
