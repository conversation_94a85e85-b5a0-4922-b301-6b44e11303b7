#!/usr/bin/env python3
"""
重启系统并进行快速测试
"""

import os
import sys
import subprocess
import time
import asyncio
from pathlib import Path

def print_status(message, status="INFO"):
    """打印状态信息"""
    symbols = {
        "INFO": "ℹ️",
        "SUCCESS": "✅", 
        "ERROR": "❌",
        "WARNING": "⚠️"
    }
    print(f"{symbols.get(status, 'ℹ️')} {message}")

def check_environment():
    """检查环境"""
    print_status("检查环境配置...")
    
    # 检查.env文件
    env_file = Path(".env")
    if not env_file.exists():
        print_status(".env文件不存在", "WARNING")
        return False
    
    # 检查API密钥
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        api_key = os.getenv("QWEN_API_KEY", "")
        if not api_key or api_key == "your_qwen_api_key_here":
            print_status("API密钥未配置", "WARNING")
            return False
        
        print_status(f"API密钥已配置: {api_key[:10]}...", "SUCCESS")
        return True
        
    except ImportError:
        print_status("python-dotenv未安装", "ERROR")
        return False

async def test_qwen_client():
    """测试千问3客户端"""
    print_status("测试千问3客户端...")
    
    try:
        from qwen_api_client import QwenAPIClient
        from dotenv import load_dotenv
        load_dotenv()
        
        api_key = os.getenv("QWEN_API_KEY", "")
        if not api_key:
            print_status("API密钥未配置，跳过测试", "WARNING")
            return False
        
        async with QwenAPIClient(api_key=api_key) as client:
            # 简单测试
            response = await client._call_api("测试连接", max_tokens=50)
            if response:
                print_status("千问3客户端测试成功", "SUCCESS")
                return True
            else:
                print_status("千问3客户端测试失败", "ERROR")
                return False
                
    except Exception as e:
        print_status(f"千问3客户端测试失败: {e}", "ERROR")
        return False

def main():
    """主函数"""
    print("🔄 系统修复和测试")
    print("=" * 40)
    
    # 检查环境
    env_ok = check_environment()
    
    if not env_ok:
        print_status("环境检查失败，请先配置API密钥", "ERROR")
        print("运行: python setup_qwen.py")
        return
    
    # 测试千问3客户端
    print("\n🧪 测试千问3客户端")
    print("-" * 30)
    
    try:
        client_ok = asyncio.run(test_qwen_client())
    except Exception as e:
        print_status(f"客户端测试出错: {e}", "ERROR")
        client_ok = False
    
    # 总结
    print("\n📋 修复状态总结")
    print("=" * 40)
    
    if env_ok:
        print_status("环境配置正常", "SUCCESS")
    else:
        print_status("环境配置有问题", "ERROR")
    
    if client_ok:
        print_status("千问3客户端正常", "SUCCESS")
    else:
        print_status("千问3客户端需要调试", "WARNING")
    
    print("\n🚀 修复内容:")
    print("1. ✅ 修正了API URL配置")
    print("2. ✅ 删除了旧的模拟API代码")
    print("3. ✅ 修复了临时文件路径问题")
    print("4. ✅ 更新了健康检查逻辑")
    
    if env_ok and client_ok:
        print("\n🎉 系统修复完成！现在可以正常使用AI功能")
        print("💡 运行: python start_mvp.py")
    else:
        print("\n⚠️  系统部分修复完成，但仍有问题需要解决")

if __name__ == "__main__":
    main()
