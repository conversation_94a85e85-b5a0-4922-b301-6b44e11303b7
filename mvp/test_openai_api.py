#!/usr/bin/env python3
"""
测试OpenAI兼容接口的千问3 API调用
验证修正后的API调用方式
"""

import os
import asyncio
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

async def test_openai_compatible_api():
    """测试OpenAI兼容的千问3 API"""
    print("🧪 测试OpenAI兼容的千问3 API")
    print("=" * 40)
    
    api_key = os.getenv("QWEN_API_KEY", "")
    if not api_key:
        print("❌ 未找到QWEN_API_KEY环境变量")
        return False
    
    try:
        from openai import OpenAI
        
        # 创建客户端
        client = OpenAI(
            api_key=api_key,
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1"
        )
        
        print("✓ OpenAI客户端创建成功")
        
        # 测试API调用
        print("🔄 测试API调用...")
        
        completion = client.chat.completions.create(
            model="qwen-plus",
            messages=[
                {'role': 'system', 'content': '你是一个专业的合同分析专家。'},
                {'role': 'user', 'content': '请简单介绍一下合同审核的重要性，用一句话回答。'}
            ],
            max_tokens=100,
            temperature=0.1
        )
        
        if completion.choices and len(completion.choices) > 0:
            content = completion.choices[0].message.content
            print("✅ API调用成功！")
            print(f"📝 响应内容: {content}")
            print(f"📊 Token使用: {completion.usage}")
            return True
        else:
            print("❌ API调用失败：没有返回内容")
            return False
            
    except Exception as e:
        print(f"❌ API调用失败: {e}")
        return False

async def test_qwen_client():
    """测试我们的QwenAPIClient"""
    print("\n🔧 测试QwenAPIClient")
    print("=" * 40)
    
    api_key = os.getenv("QWEN_API_KEY", "")
    if not api_key:
        print("❌ 未找到QWEN_API_KEY环境变量")
        return False
    
    try:
        from qwen_api_client import QwenAPIClient
        
        async with QwenAPIClient(api_key=api_key) as client:
            print("✓ QwenAPIClient创建成功")
            
            # 测试合同类型识别
            test_contract = """
            技术开发合同
            
            甲方：北京科技有限公司
            乙方：上海软件开发有限公司
            
            合同金额：人民币500,000元整
            """
            
            result = await client.analyze_contract_type(
                document_content=test_contract,
                filename="技术开发合同.docx"
            )
            
            print("✅ 合同类型识别成功！")
            print(f"📋 识别结果: {result}")
            return True
            
    except Exception as e:
        print(f"❌ QwenAPIClient测试失败: {e}")
        return False

async def main():
    """主函数"""
    print("🤖 千问3 API修正验证")
    print()
    
    # 测试原生OpenAI接口
    openai_ok = await test_openai_compatible_api()
    
    # 测试我们的客户端
    client_ok = await test_qwen_client()
    
    print("\n" + "=" * 50)
    print("📋 测试结果总结")
    print("=" * 50)
    
    if openai_ok:
        print("✅ OpenAI兼容接口调用正常")
    else:
        print("❌ OpenAI兼容接口调用失败")
    
    if client_ok:
        print("✅ QwenAPIClient工作正常")
    else:
        print("❌ QwenAPIClient需要调试")
    
    if openai_ok and client_ok:
        print("\n🎉 所有测试通过！千问3 API集成修正成功")
    else:
        print("\n⚠️  部分测试失败，需要进一步调试")

if __name__ == "__main__":
    asyncio.run(main())
