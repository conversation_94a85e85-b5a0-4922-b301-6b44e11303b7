#!/usr/bin/env python3
"""
调试OpenAI客户端URL构造问题
"""

import os
from dotenv import load_dotenv
load_dotenv()

def test_openai_client_url():
    """测试OpenAI客户端URL构造"""
    print("🔍 调试OpenAI客户端URL构造")
    print("=" * 40)
    
    try:
        from openai import OpenAI
        
        api_key = os.getenv("QWEN_API_KEY", "test_key")
        base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
        
        print(f"API密钥: {api_key[:10]}...")
        print(f"Base URL: {base_url}")
        
        # 创建客户端
        client = OpenAI(
            api_key=api_key,
            base_url=base_url
        )
        
        print(f"客户端Base URL: {client.base_url}")
        
        # 检查客户端内部URL构造
        print("\n🔧 尝试调用API...")
        
        try:
            completion = client.chat.completions.create(
                model="qwen-plus",
                messages=[
                    {'role': 'user', 'content': '测试'}
                ],
                max_tokens=10
            )
            print("✅ API调用成功")
            print(f"响应: {completion.choices[0].message.content}")
            
        except Exception as e:
            print(f"❌ API调用失败: {e}")
            
            # 分析错误信息
            error_str = str(e)
            if "No static resource" in error_str:
                print("\n🔍 错误分析:")
                print("- 错误表明URL路径不正确")
                print("- OpenAI SDK可能在base_url后添加了错误的路径")
                
                # 检查实际请求的URL
                if "api/v1/aigc/text-generation/generation/chat/completions" in error_str:
                    print("- 实际请求URL包含了错误的路径")
                    print("- 问题：OpenAI SDK没有正确处理DashScope的兼容模式URL")
                    
                    print("\n💡 解决方案:")
                    print("1. 检查OpenAI SDK版本")
                    print("2. 确认DashScope兼容模式的正确URL格式")
                    print("3. 可能需要使用不同的base_url格式")
        
    except ImportError:
        print("❌ OpenAI SDK未安装")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_different_urls():
    """测试不同的URL格式"""
    print("\n🧪 测试不同的URL格式")
    print("=" * 40)
    
    urls_to_test = [
        "https://dashscope.aliyuncs.com/compatible-mode/v1",
        "https://dashscope.aliyuncs.com/compatible-mode/v1/",
        "https://dashscope.aliyuncs.com/compatible-mode",
    ]
    
    for url in urls_to_test:
        print(f"\n测试URL: {url}")
        try:
            from openai import OpenAI
            
            client = OpenAI(
                api_key="test_key",
                base_url=url
            )
            
            print(f"客户端Base URL: {client.base_url}")
            
        except Exception as e:
            print(f"创建客户端失败: {e}")

if __name__ == "__main__":
    test_openai_client_url()
    test_different_urls()
