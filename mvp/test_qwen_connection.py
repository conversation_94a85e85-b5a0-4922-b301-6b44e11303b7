#!/usr/bin/env python3
"""
千问3 API连接测试脚本
验证API密钥和连接是否正常
"""

import asyncio
import os
import sys
from pathlib import Path

# 加载环境变量
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("⚠️  python-dotenv未安装，请运行: pip install python-dotenv")

async def test_qwen_connection():
    """测试千问3 API连接"""
    print("🧪 千问3 API连接测试")
    print("=" * 40)
    
    # 检查API密钥
    api_key = os.getenv("QWEN_API_KEY", "")
    if not api_key:
        print("❌ 未找到QWEN_API_KEY环境变量")
        print("请先运行: python setup_qwen.py")
        return False
    
    if api_key == "your_qwen_api_key_here":
        print("❌ API密钥未配置，仍为示例值")
        print("请先运行: python setup_qwen.py")
        return False
    
    print(f"✓ API密钥已配置: {api_key[:10]}...")
    
    # 测试API调用
    try:
        from qwen_api_client import QwenAPIClient
        
        print("✓ 千问3客户端模块导入成功")
        
        # 创建客户端
        async with QwenAPIClient(api_key=api_key) as client:
            print("✓ 千问3客户端创建成功")
            
            # 测试简单的API调用
            print("🔄 测试API调用...")
            
            test_prompt = "请简单介绍一下你自己，用一句话回答。"
            
            try:
                response = await client._call_api(test_prompt, max_tokens=100)

                if response and len(response.strip()) > 0:
                    print("✅ API调用成功！")
                    print(f"📝 响应内容: {response[:100]}...")
                    print(f"📊 响应长度: {len(response)} 字符")
                    return True
                else:
                    print("❌ API调用失败：响应为空")
                    return False
                    
            except Exception as api_error:
                print(f"❌ API调用失败: {api_error}")
                
                # 提供常见错误的解决建议
                error_str = str(api_error).lower()
                if "unauthorized" in error_str or "401" in error_str:
                    print("💡 建议：API密钥可能无效，请检查密钥是否正确")
                elif "forbidden" in error_str or "403" in error_str:
                    print("💡 建议：API密钥可能没有权限，请检查DashScope服务是否已开通")
                elif "rate limit" in error_str or "429" in error_str:
                    print("💡 建议：API调用频率过高，请稍后重试")
                elif "timeout" in error_str:
                    print("💡 建议：网络超时，请检查网络连接")
                else:
                    print("💡 建议：请检查网络连接和API密钥配置")
                
                return False
                
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        print("请确保已安装所有依赖: pip install -r requirements.txt")
        return False
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        return False

async def test_contract_analysis():
    """测试合同分析功能"""
    print("\n🔍 合同分析功能测试")
    print("=" * 40)
    
    try:
        from qwen_api_client import QwenAPIClient
        
        api_key = os.getenv("QWEN_API_KEY", "")
        if not api_key or api_key == "your_qwen_api_key_here":
            print("⚠️  跳过合同分析测试（API密钥未配置）")
            return True
        
        # 测试合同内容
        test_contract = """
        技术开发合同
        
        甲方：北京科技有限公司
        乙方：上海软件开发有限公司
        
        合同金额：人民币500,000元整
        开发周期：6个月
        签署日期：2024年6月1日
        """
        
        async with QwenAPIClient(api_key=api_key) as client:
            print("🔄 测试合同类型识别...")
            
            try:
                result = await client.analyze_contract_type(
                    document_content=test_contract,
                    filename="技术开发合同.docx"
                )
                
                print(f"✅ 合同类型识别成功: {result.get('contract_type', 'unknown')}")
                print(f"📊 置信度: {result.get('confidence', 0):.2f}")
                
                return True
                
            except Exception as e:
                print(f"❌ 合同分析测试失败: {e}")
                return False
                
    except Exception as e:
        print(f"❌ 合同分析测试出现错误: {e}")
        return False

def print_summary(connection_ok, analysis_ok):
    """打印测试总结"""
    print("\n" + "=" * 50)
    print("📋 测试结果总结")
    print("=" * 50)
    
    if connection_ok:
        print("✅ 千问3 API连接正常")
    else:
        print("❌ 千问3 API连接失败")
    
    if analysis_ok:
        print("✅ 合同分析功能正常")
    else:
        print("⚠️  合同分析功能测试跳过或失败")
    
    print()
    
    if connection_ok and analysis_ok:
        print("🎉 所有测试通过！系统已准备就绪")
        print("💡 现在可以运行: python start_mvp.py")
    elif connection_ok:
        print("⚠️  基础连接正常，但合同分析功能可能需要调试")
        print("💡 可以尝试运行系统: python start_mvp.py")
    else:
        print("❌ 系统未准备就绪，请检查配置")
        print("💡 建议运行配置向导: python setup_qwen.py")

async def main():
    """主函数"""
    print("🤖 AI合同审核系统 - 千问3连接测试")
    print()
    
    # 测试API连接
    connection_ok = await test_qwen_connection()
    
    # 测试合同分析
    analysis_ok = await test_contract_analysis()
    
    # 打印总结
    print_summary(connection_ok, analysis_ok)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n测试已取消")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 测试过程中出现未预期的错误: {e}")
        sys.exit(1)
