"""
千问3 API客户端
负责与千问3大语言模型的API交互，提供合同智能分析能力
基于OpenAI兼容接口
"""

import asyncio
import logging
import json
import os
from typing import Dict, List, Optional, Any
from datetime import datetime
import time

try:
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    OpenAI = None

logger = logging.getLogger(__name__)

class QwenAPIClient:
    """千问3 API客户端 - 基于OpenAI兼容接口"""

    def __init__(self, api_key: str, base_url: str = None):
        if not OPENAI_AVAILABLE:
            raise ImportError("OpenAI SDK未安装，请运行: pip install openai")

        self.api_key = api_key
        self.base_url = base_url or "https://dashscope.aliyuncs.com/compatible-mode/v1"
        self.model_name = "qwen-plus"  # 使用qwen-plus模型

        # API配置
        self.timeout = 60
        self.max_retries = 3
        self.rate_limit_delay = 1

        # 创建OpenAI客户端
        self.client = OpenAI(
            api_key=api_key,
            base_url=self.base_url,
            timeout=self.timeout
        )
    
    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        # OpenAI客户端不需要显式关闭
        pass
    
    async def analyze_contract_type(self, document_content: str, filename: str) -> Dict[str, Any]:
        """
        分析合同类型

        Args:
            document_content: 合同文档内容
            filename: 文件名

        Returns:
            包含合同类型和置信度的字典
        """
        logger.info(f"🔍 开始分析合同类型 - 文件: {filename}")
        logger.info(f"📄 文档内容长度: {len(document_content)} 字符")
        prompt = f"""
请分析以下合同文档，判断其合同类型。

文件名：{filename}

合同内容（前2000字符）：
{document_content[:2000]}

请从以下类型中选择最合适的一个：
1. sales - 销售合同（买卖、采购、商品交易）
2. service - 服务合同（技术服务、咨询、开发、维护）
3. lease - 租赁合同（房屋、设备、场地租赁）
4. employment - 劳动合同（雇佣、聘用关系）
5. general - 通用合同（其他类型）

请以JSON格式回复：
{{
    "contract_type": "类型代码",
    "confidence": 0.95,
    "reasoning": "判断理由"
}}
"""
        
        try:
            logger.info("🚀 发送合同类型分析请求到千问3...")
            response = await self._call_api(prompt, max_tokens=500)

            result = self._parse_contract_type_response(response)
            logger.info(f"✅ 合同类型分析完成: {result.get('contract_type', 'unknown')} (置信度: {result.get('confidence', 0):.2f})")
            return result

        except Exception as e:
            logger.error(f"❌ 合同类型分析失败: {e}")
            fallback_result = {
                "contract_type": "general",
                "confidence": 0.5,
                "reasoning": "API调用失败，使用默认类型"
            }
            logger.warning(f"🔄 使用降级结果: {fallback_result}")
            return fallback_result
    
    async def extract_contract_elements(self, document_content: str, contract_type: str) -> Dict[str, Any]:
        """
        提取合同要素

        Args:
            document_content: 合同文档内容
            contract_type: 合同类型

        Returns:
            提取的要素列表
        """
        logger.info(f"🔍 开始提取合同要素 - 合同类型: {contract_type}")
        logger.info(f"📄 文档内容长度: {len(document_content)} 字符")
        # 智能内容选择 - 确保重要法律条款不被遗漏
        selected_content = self._smart_content_selection(document_content)

        prompt = f"""
请从以下{contract_type}类型合同中提取关键要素信息。

合同内容：
{selected_content}

请提取以下要素（如果存在）：
1. 合同标题
2. 甲方信息（公司名称、地址、联系方式）
3. 乙方信息（公司名称、地址、联系方式）
4. 合同金额（总价、单价、付款方式）
5. 具体日期（请明确标识日期类型）：
   - 合同签署日期
   - 合同生效日期
   - 合同到期日期
   - 项目开始日期
   - 项目完成日期
   - 其他重要日期
6. 核心条款（主要权利义务）
7. 违约责任
8. 争议解决方式

重要提示：
- 对于日期，请明确标识具体是什么日期，不要只说"重要日期"
- 甲方乙方请提取公司名称，不要提取保密条款或其他无关内容
- 如果是"委托方（甲方）"格式，请提取括号后的公司名称
- 特别注意争议解决条款，通常在合同末尾，包含仲裁、诉讼等解决方式

请以JSON格式回复：
{{
    "elements": [
        {{
            "element_name": "具体要素名称（如：合同签署日期、甲方公司名称等）",
            "category": "party_info/financial_terms/time_terms/contract_terms",
            "value": "提取的具体内容",
            "confidence": 0.95,
            "source_location": "在文档中的大致位置描述"
        }}
    ]
}}
"""
        
        try:
            logger.info("🚀 发送要素提取请求到千问3...")
            response = await self._call_api(prompt, max_tokens=2000)

            result = self._parse_elements_response(response)
            element_count = len(result.get("elements", []))
            logger.info(f"✅ 要素提取完成: 成功提取 {element_count} 个要素")

            # 记录提取的要素概要
            if element_count > 0:
                for i, element in enumerate(result.get("elements", [])[:5]):  # 只显示前5个
                    logger.info(f"  要素 {i+1}: {element.get('element_name', 'N/A')} = {element.get('value', 'N/A')[:50]}...")
                if element_count > 5:
                    logger.info(f"  ... 还有 {element_count - 5} 个要素")

            return result

        except Exception as e:
            logger.error(f"❌ 要素提取失败: {e}")
            fallback_result = {"elements": []}
            logger.warning(f"🔄 使用降级结果: {fallback_result}")
            return fallback_result

    async def extract_semantic_elements(self, document_content: str, extraction_focus: str = "semantic_terms") -> Dict[str, Any]:
        """
        提取语义要素 - 专门用于分层提取策略

        Args:
            document_content: 合同文档内容
            extraction_focus: 提取重点 ("semantic_terms", "legal_clauses", "risk_terms")

        Returns:
            提取的语义要素列表
        """
        logger.info(f"🧠 开始语义要素提取 - 重点: {extraction_focus}")
        logger.info(f"📄 文档内容长度: {len(document_content)} 字符")

        # 根据提取重点构建不同的提示
        if extraction_focus == "semantic_terms":
            prompt = self._build_semantic_terms_prompt(document_content)
        elif extraction_focus == "legal_clauses":
            prompt = self._build_legal_clauses_prompt(document_content)
        elif extraction_focus == "risk_terms":
            prompt = self._build_risk_terms_prompt(document_content)
        else:
            prompt = self._build_semantic_terms_prompt(document_content)

        try:
            logger.info("🚀 发送语义要素提取请求到千问3...")
            response = await self._call_api(prompt, max_tokens=1500)

            result = self._parse_elements_response(response)
            element_count = len(result.get("elements", []))
            logger.info(f"✅ 语义要素提取完成: 成功提取 {element_count} 个要素")

            # 记录提取的语义要素概要
            if element_count > 0:
                for i, element in enumerate(result.get("elements", [])[:3]):  # 只显示前3个
                    logger.info(f"  语义要素 {i+1}: {element.get('element_name', 'N/A')}")
                if element_count > 3:
                    logger.info(f"  ... 还有 {element_count - 3} 个语义要素")

            return result

        except Exception as e:
            logger.error(f"❌ 语义要素提取失败: {e}")
            fallback_result = {"elements": []}
            logger.warning(f"🔄 使用降级结果: {fallback_result}")
            return fallback_result

    def _build_semantic_terms_prompt(self, document_content: str) -> str:
        """构建语义条款提取提示"""
        return f"""
请从以下合同内容中提取需要语义理解的复杂要素，专注于：

1. 权利义务条款 - 双方的核心权利和义务关系
2. 履约条件 - 合同履行的前提和条件
3. 违约责任 - 违约情况的定义和责任承担
4. 争议解决 - 纠纷处理的具体方式和程序
5. 特殊约定 - 非标准的特殊条款和约定
6. 终止条款 - 合同终止的条件和程序

注意：请忽略简单的数字、日期、姓名等结构化信息，专注于需要理解和解释的条款内容。

合同内容：
{document_content[:2500]}

请以JSON格式回复：
{{
    "elements": [
        {{
            "element_name": "要素名称",
            "category": "contract_terms",
            "value": "提取的具体内容（保持原文表述）",
            "confidence": 0.85,
            "source_location": "在文档中的大致位置"
        }}
    ]
}}
"""

    def _build_legal_clauses_prompt(self, document_content: str) -> str:
        """构建法律条款提取提示"""
        return f"""
请从以下合同内容中提取法律相关的重要条款：

1. 法律适用 - 适用的法律法规
2. 管辖权 - 法院管辖权约定
3. 合规要求 - 需要遵守的法规要求
4. 知识产权 - 知识产权归属和保护
5. 保密条款 - 保密义务和范围
6. 不可抗力 - 不可抗力的定义和处理

合同内容：
{document_content[:2500]}

请以JSON格式回复，重点提取法律层面的条款。
"""

    def _build_risk_terms_prompt(self, document_content: str) -> str:
        """构建风险条款提取提示"""
        return f"""
请从以下合同内容中识别潜在的风险相关条款：

1. 责任限制 - 责任限制和免责条款
2. 赔偿条款 - 损失赔偿的范围和标准
3. 保证条款 - 各方提供的保证和承诺
4. 风险分担 - 风险承担的分配
5. 保险要求 - 保险购买和维持要求
6. 担保条款 - 担保方式和担保责任

合同内容：
{document_content[:2500]}

请以JSON格式回复，重点识别风险管理相关的条款。
"""
    
    async def detect_missing_clauses(self, document_content: str, contract_type: str) -> Dict[str, Any]:
        """
        检测缺失条款
        
        Args:
            document_content: 合同文档内容
            contract_type: 合同类型
            
        Returns:
            缺失条款列表
        """
        clause_requirements = {
            "sales": ["商品描述", "价格条款", "交付条款", "付款方式", "违约责任", "质量保证"],
            "service": ["服务内容", "服务标准", "服务期限", "费用支付", "知识产权", "保密条款"],
            "lease": ["租赁物描述", "租赁期限", "租金标准", "押金条款", "维修责任", "违约处理"],
            "employment": ["工作内容", "工作地点", "工作时间", "薪酬福利", "保密条款", "竞业限制"],
            "general": ["合同主体", "权利义务", "履行期限", "违约责任", "争议解决"]
        }
        
        required_clauses = clause_requirements.get(contract_type, clause_requirements["general"])
        
        prompt = f"""
请检查以下{contract_type}类型合同是否包含必要条款。

合同内容：
{document_content[:3000]}

必要条款清单：
{', '.join(required_clauses)}

请分析合同中是否包含上述条款，如果缺失，请指出缺失的条款及其重要性。

请以JSON格式回复：
{{
    "missing_clauses": [
        {{
            "clause_name": "缺失条款名称",
            "clause_type": "条款类型",
            "importance_level": "high/medium/low",
            "description": "条款描述",
            "recommendation": "建议补充的内容"
        }}
    ]
}}
"""
        
        try:
            response = await self._call_api(prompt, max_tokens=1500)
            return self._parse_missing_clauses_response(response)
        except Exception as e:
            logger.error(f"缺失条款检测失败: {e}")
            return {"missing_clauses": []}
    
    async def identify_risk_points(self, document_content: str, contract_type: str) -> Dict[str, Any]:
        """
        识别风险点
        
        Args:
            document_content: 合同文档内容
            contract_type: 合同类型
            
        Returns:
            风险点列表
        """
        prompt = f"""
请分析以下{contract_type}类型合同中的潜在风险点。

合同内容：
{document_content[:3000]}

请重点关注以下风险类型：
1. 条款表述不清晰或有歧义
2. 权利义务不对等
3. 违约责任不明确或过轻
4. 缺少必要的保护条款
5. 法律合规风险
6. 履约风险
7. 财务风险

请以JSON格式回复：
{{
    "risk_points": [
        {{
            "risk_id": "R001",
            "risk_type": "风险类型",
            "risk_level": "high/medium/low",
            "description": "风险描述",
            "location": "风险位置",
            "suggestion": "处理建议",
            "related_clause": "相关条款"
        }}
    ]
}}
"""
        
        try:
            response = await self._call_api(prompt, max_tokens=2000)
            return self._parse_risk_points_response(response)
        except Exception as e:
            logger.error(f"风险识别失败: {e}")
            return {"risk_points": []}
    
    async def _call_api(self, prompt: str, max_tokens: int = 1500) -> str:
        """
        调用千问3 API

        Args:
            prompt: 提示词
            max_tokens: 最大token数

        Returns:
            API响应内容
        """
        messages = [
            {
                "role": "system",
                "content": "你是一个专业的合同分析专家，具有丰富的法律知识和合同审核经验。请仔细分析合同内容，提供准确、专业的分析结果。"
            },
            {
                "role": "user",
                "content": prompt
            }
        ]
        
        for attempt in range(self.max_retries):
            try:
                logger.info(f"调用千问3 API (尝试 {attempt + 1}/{self.max_retries})")

                # 记录输入内容
                logger.info("=" * 60)
                logger.info("📤 千问3 API 输入内容:")
                logger.info(f"模型: {self.model_name}")
                logger.info(f"最大Token数: {max_tokens}")
                logger.info(f"Temperature: 0.1")
                logger.info(f"消息数量: {len(messages)}")

                for i, message in enumerate(messages):
                    role = message.get('role', 'unknown')
                    content = message.get('content', '')
                    logger.info(f"消息 {i+1} [{role}]: {content[:200]}{'...' if len(content) > 200 else ''}")

                logger.info("=" * 60)

                # 使用OpenAI兼容接口调用
                completion = self.client.chat.completions.create(
                    model=self.model_name,
                    messages=messages,
                    max_tokens=max_tokens,
                    temperature=0.1,  # 降低随机性，提高一致性
                    top_p=0.8
                )

                # 解析响应
                if completion.choices and len(completion.choices) > 0:
                    content = completion.choices[0].message.content
                    if content:
                        # 记录输出内容
                        logger.info("📥 千问3 API 输出内容:")
                        logger.info(f"响应长度: {len(content)} 字符")
                        logger.info(f"完整响应: {content}")

                        # 记录Token使用情况
                        if hasattr(completion, 'usage') and completion.usage:
                            usage = completion.usage
                            logger.info(f"Token使用情况:")
                            logger.info(f"  - 输入Token: {getattr(usage, 'prompt_tokens', 'N/A')}")
                            logger.info(f"  - 输出Token: {getattr(usage, 'completion_tokens', 'N/A')}")
                            logger.info(f"  - 总Token: {getattr(usage, 'total_tokens', 'N/A')}")

                        logger.info("=" * 60)
                        logger.info("✅ API调用成功")

                        return content
                    else:
                        raise Exception("API返回内容为空")
                else:
                    raise Exception("API返回格式错误：没有choices")

            except Exception as e:
                error_str = str(e).lower()

                # 记录错误详情
                logger.error("❌ 千问3 API 调用失败:")
                logger.error(f"错误类型: {type(e).__name__}")
                logger.error(f"错误信息: {str(e)}")
                logger.error("=" * 60)

                # 处理速率限制
                if "rate limit" in error_str or "429" in error_str:
                    wait_time = (2 ** attempt) * self.rate_limit_delay
                    logger.warning(f"⏳ API速率限制，等待 {wait_time} 秒后重试")
                    await asyncio.sleep(wait_time)
                    continue

                logger.warning(f"🔄 API调用失败 (尝试 {attempt + 1}/{self.max_retries})")
                if attempt == self.max_retries - 1:
                    logger.error(f"💥 所有重试已用完，最终失败: {e}")
                    raise

                retry_delay = 2 ** attempt
                logger.info(f"⏱️  等待 {retry_delay} 秒后重试...")
                await asyncio.sleep(retry_delay)

        raise Exception("API调用重试次数已用完")
    
    def _parse_contract_type_response(self, response: str) -> Dict[str, Any]:
        """解析合同类型分析响应"""
        try:
            # 尝试提取JSON
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            if json_start >= 0 and json_end > json_start:
                json_str = response[json_start:json_end]
                result = json.loads(json_str)
                return result
        except:
            pass
        
        # 如果JSON解析失败，使用文本分析
        response_lower = response.lower()
        if "sales" in response_lower or "销售" in response_lower:
            contract_type = "sales"
        elif "service" in response_lower or "服务" in response_lower or "开发" in response_lower:
            contract_type = "service"
        elif "lease" in response_lower or "租赁" in response_lower:
            contract_type = "lease"
        elif "employment" in response_lower or "劳动" in response_lower:
            contract_type = "employment"
        else:
            contract_type = "general"
        
        return {
            "contract_type": contract_type,
            "confidence": 0.7,
            "reasoning": "基于文本关键词分析"
        }
    
    def _parse_elements_response(self, response: str) -> Dict[str, Any]:
        """解析要素提取响应"""
        try:
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            if json_start >= 0 and json_end > json_start:
                json_str = response[json_start:json_end]
                result = json.loads(json_str)
                return result
        except:
            pass
        
        return {"elements": []}
    
    def _parse_missing_clauses_response(self, response: str) -> Dict[str, Any]:
        """解析缺失条款响应"""
        try:
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            if json_start >= 0 and json_end > json_start:
                json_str = response[json_start:json_end]
                result = json.loads(json_str)
                return result
        except:
            pass
        
        return {"missing_clauses": []}
    
    def _parse_risk_points_response(self, response: str) -> Dict[str, Any]:
        """解析风险点响应"""
        try:
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            if json_start >= 0 and json_end > json_start:
                json_str = response[json_start:json_end]
                result = json.loads(json_str)
                return result
        except:
            pass
        
        return {"risk_points": []}

    def _smart_content_selection(self, document_content: str, max_length: int = 4500) -> str:
        """
        智能内容选择 - 确保重要法律条款不被遗漏

        策略：
        1. 优先保留合同开头（当事方信息）
        2. 优先保留合同末尾（法律条款）
        3. 中间部分按重要性选择
        """
        if len(document_content) <= max_length:
            return document_content

        logger.info(f"📄 文档过长({len(document_content)}字符)，开始智能内容选择")

        # 分割文档为段落
        paragraphs = document_content.split('\n')
        paragraphs = [p.strip() for p in paragraphs if p.strip()]

        # 计算每个段落的重要性分数
        paragraph_scores = []
        for i, paragraph in enumerate(paragraphs):
            score = self._calculate_paragraph_importance(paragraph, i, len(paragraphs))
            paragraph_scores.append((i, paragraph, score))

        # 按重要性排序
        paragraph_scores.sort(key=lambda x: x[2], reverse=True)

        # 选择重要段落，确保不超过长度限制
        selected_paragraphs = []
        current_length = 0

        for i, paragraph, score in paragraph_scores:
            if current_length + len(paragraph) + 1 <= max_length:  # +1 for newline
                selected_paragraphs.append((i, paragraph))
                current_length += len(paragraph) + 1
            else:
                break

        # 按原始顺序重新排列
        selected_paragraphs.sort(key=lambda x: x[0])

        selected_content = '\n'.join([p[1] for p in selected_paragraphs])

        logger.info(f"✅ 智能选择完成: {len(selected_paragraphs)}/{len(paragraphs)} 段落, {len(selected_content)} 字符")

        return selected_content

    def _calculate_paragraph_importance(self, paragraph: str, position: int, total_paragraphs: int) -> float:
        """计算段落重要性分数"""
        score = 0.0
        paragraph_lower = paragraph.lower()

        # 1. 位置权重 (30%)
        if position < total_paragraphs * 0.2:  # 前20%
            score += 0.3
        elif position > total_paragraphs * 0.8:  # 后20%
            score += 0.25  # 法律条款通常在末尾
        else:
            score += 0.1

        # 2. 关键词权重 (70%)

        # 当事方信息 (高重要性)
        party_keywords = ['甲方', '乙方', '委托方', '受托方', '出租方', '承租方', '买方', '卖方']
        if any(keyword in paragraph for keyword in party_keywords):
            score += 0.2

        # 金额信息 (高重要性)
        financial_keywords = ['金额', '价格', '费用', '付款', '支付', '元', '万元', '¥', '$']
        if any(keyword in paragraph for keyword in financial_keywords):
            score += 0.15

        # 日期信息 (中等重要性)
        date_keywords = ['日期', '年', '月', '日', '签署', '生效', '到期', '完成']
        if any(keyword in paragraph for keyword in date_keywords):
            score += 0.1

        # 法律条款 (高重要性)
        legal_keywords = [
            '争议', '仲裁', '诉讼', '法院', '违约', '责任', '赔偿', '解除', '终止',
            '知识产权', '保密', '不可抗力', '管辖', '适用法律'
        ]
        legal_count = sum(1 for keyword in legal_keywords if keyword in paragraph_lower)
        score += min(legal_count * 0.05, 0.2)  # 最多加0.2分

        # 条款编号 (表示正式条款)
        if any(pattern in paragraph for pattern in ['第', '条', '款', '项']):
            score += 0.05

        # 长度权重 (适中长度更重要)
        if 20 <= len(paragraph) <= 200:
            score += 0.05
        elif len(paragraph) > 500:
            score -= 0.05  # 过长的段落可能不重要

        return min(score, 1.0)  # 限制最高分为1.0
