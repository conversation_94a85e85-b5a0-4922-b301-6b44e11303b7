#!/usr/bin/env python3
"""
测试详细日志输出
验证千问3 API调用的输入输出日志
"""

import asyncio
import logging
import os
from dotenv import load_dotenv

# 配置详细日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 加载环境变量
load_dotenv()

async def test_detailed_logging():
    """测试详细日志输出"""
    print("🔍 测试千问3 API详细日志输出")
    print("=" * 50)
    
    api_key = os.getenv("QWEN_API_KEY", "")
    if not api_key:
        print("❌ 未找到API密钥")
        return
    
    try:
        from qwen_api_client import QwenAPIClient
        
        # 创建客户端
        async with QwenAPIClient(api_key=api_key) as client:
            print("✅ 千问3客户端创建成功")
            
            # 测试合同类型识别（会显示详细的输入输出日志）
            print("\n🧪 测试合同类型识别...")
            
            test_contract = """
            软件开发服务合同
            
            甲方：北京科技有限公司
            地址：北京市海淀区中关村大街1号
            
            乙方：上海软件开发有限公司  
            地址：上海市浦东新区张江高科技园区
            
            项目内容：开发企业管理系统
            合同金额：人民币500,000元整
            开发周期：6个月
            签署日期：2024年6月1日
            
            付款方式：
            1. 签约后支付30%
            2. 项目完成50%后支付40%
            3. 验收合格后支付剩余30%
            """
            
            result = await client.analyze_contract_type(
                document_content=test_contract,
                filename="软件开发服务合同.docx"
            )
            
            print(f"\n📋 分析结果: {result}")
            
            # 测试要素提取
            print("\n🧪 测试要素提取...")
            
            elements_result = await client.extract_contract_elements(
                document_content=test_contract,
                contract_type="service"
            )
            
            print(f"\n📋 要素提取结果: {elements_result}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

async def test_contract_analyzer_logs():
    """测试合同分析器的日志输出"""
    print("\n🔧 测试合同分析器日志输出")
    print("=" * 50)
    
    try:
        from contract_analyzer import ContractAnalyzer
        from document_processor import DocumentProcessor
        from models import DocumentInfo
        from docx import Document
        import tempfile
        
        # 创建测试文档
        doc = Document()
        doc.add_heading('技术开发合同', 0)
        doc.add_paragraph('甲方：北京科技有限公司')
        doc.add_paragraph('乙方：上海软件开发有限公司')
        doc.add_paragraph('合同金额：人民币500,000元整')
        doc.add_paragraph('开发周期：6个月')
        
        # 保存到临时文件
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.docx')
        doc.save(temp_file.name)
        
        try:
            # 处理文档
            processor = DocumentProcessor()
            document_info = await processor.process_document(temp_file.name)
            document_info.filename = "技术开发合同.docx"
            
            # 分析合同
            analyzer = ContractAnalyzer()
            result = await analyzer.analyze_contract(
                document_info=document_info,
                original_filename="技术开发合同.docx",
                temp_file_path=temp_file.name
            )
            
            print(f"\n📋 完整分析结果:")
            print(f"合同类型: {result.contract_type}")
            print(f"处理时间: {result.processing_duration:.2f}秒")
            print(f"提取要素数量: {len(result.extracted_elements)}")
            print(f"整体置信度: {result.overall_confidence:.2f}")
            
        finally:
            # 清理临时文件
            os.unlink(temp_file.name)
            
    except Exception as e:
        print(f"❌ 合同分析器测试失败: {e}")

async def main():
    """主函数"""
    print("📊 千问3 API详细日志测试")
    print("这个测试将显示完整的API输入输出日志")
    print()
    
    # 测试API客户端日志
    await test_detailed_logging()
    
    # 测试合同分析器日志
    await test_contract_analyzer_logs()
    
    print("\n" + "=" * 60)
    print("📋 测试完成")
    print("=" * 60)
    print("现在您可以在日志中看到:")
    print("✅ 千问3 API的完整输入内容")
    print("✅ 千问3 API的完整输出内容")
    print("✅ Token使用情况统计")
    print("✅ 错误处理和重试过程")
    print("✅ 要素提取的详细过程")

if __name__ == "__main__":
    asyncio.run(main())
