# AI合同审核系统 MVP 配置文件

# 服务器配置
HOST = "0.0.0.0"
PORT = 8000
DEBUG = True

# 文件处理配置
MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
SUPPORTED_FORMATS = [".doc", ".docx"]
TEMP_DIR = "./temp"

# AI API配置 (模拟)
AI_API_BASE_URL = "https://api.example.com/v1"
AI_API_KEY = "mock_api_key"
AI_API_TIMEOUT = 30
AI_MAX_RETRIES = 3

# 日志配置
LOG_LEVEL = "INFO"
LOG_DIR = "./logs"

# 技术验证标志
ENABLE_AI_MOCK = True  # 启用AI API模拟
ENABLE_PERFORMANCE_LOGGING = True  # 启用性能日志
