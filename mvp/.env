# AI合同审核系统 - 环境变量配置示例
# 复制此文件为 .env 并填入真实的配置值

# ===================
# 千问3 API配置
# ===================
# 千问3 API密钥 (必填，从阿里云DashScope获取)
QWEN_API_KEY=sk-b8dd65244aa141cab1d8e361f12f6d05

# 千问3 API基础URL (可选，默认使用官方地址)
QWEN_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1

# 千问3模型名称 (可选，默认qwen-plus)
QWEN_MODEL=qwen-plus

# API超时时间(秒) (可选，默认60)
QWEN_TIMEOUT=60

# API最大重试次数 (可选，默认3)
QWEN_MAX_RETRIES=3

# API速率限制延迟(秒) (可选，默认1.0)
QWEN_RATE_LIMIT_DELAY=1.0

# ===================
# 分析功能配置
# ===================
# 是否启用AI分析 (true/false，默认true)
ENABLE_AI_ANALYSIS=true

# 是否启用规则降级 (true/false，默认true)
FALLBACK_TO_RULES=true

# 置信度阈值 (0.0-1.0，默认0.7)
CONFIDENCE_THRESHOLD=0.7

# 发送给AI的最大内容长度 (默认10000字符)
MAX_CONTENT_LENGTH=10000

# ===================
# 服务器配置
# ===================
# 服务器主机 (默认0.0.0.0)
HOST=0.0.0.0

# 服务器端口 (默认8000)
PORT=8000

# 调试模式 (true/false，默认true)
DEBUG=true

# 热重载 (true/false，默认true)
RELOAD=true

# ===================
# 文件处理配置
# ===================
# 最大文件大小(字节) (默认50MB)
MAX_FILE_SIZE=52428800

# 临时文件目录 (默认./temp)
TEMP_DIR=./temp

# 文件上传超时时间(秒) (默认300)
UPLOAD_TIMEOUT=300

# ===================
# 日志配置
# ===================
# 日志级别 (DEBUG/INFO/WARNING/ERROR，默认INFO)
LOG_LEVEL=INFO

# 日志目录 (默认./logs)
LOG_DIR=./logs

# 日志文件最大大小(字节) (默认10MB)
LOG_MAX_FILE_SIZE=10485760

# 日志文件备份数量 (默认5)
LOG_BACKUP_COUNT=5

# ===================
# 安全配置
# ===================
# CORS允许的源 (逗号分隔，默认*)
CORS_ORIGINS=*

# 每分钟最大请求数 (默认60)
MAX_REQUESTS_PER_MINUTE=60

# 是否启用API密钥认证 (true/false，默认false)
ENABLE_API_KEY_AUTH=false

# API密钥 (如果启用认证)
API_KEY=your_api_key_here
