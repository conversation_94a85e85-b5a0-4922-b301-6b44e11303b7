"""
文档处理器 - 核心技术验证
基于python-docx的Word文档解析和文本提取功能
"""

import asyncio
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from datetime import datetime
import re
import jieba

from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT

from models import DocumentInfo, ExtractedElement, ElementCategory

logger = logging.getLogger(__name__)

class DocumentProcessor:
    """文档处理器 - 负责Word文档的解析和结构化处理"""
    
    def __init__(self):
        self.supported_formats = ['.doc', '.docx']
        self.max_file_size = 50 * 1024 * 1024  # 50MB
        
    async def health_check(self) -> Dict[str, str]:
        """健康检查"""
        try:
            # 测试python-docx基本功能
            test_doc = Document()
            test_doc.add_paragraph("健康检查测试")
            return {"status": "healthy", "component": "document_processor"}
        except Exception as e:
            logger.error(f"文档处理器健康检查失败: {e}")
            return {"status": "unhealthy", "error": str(e)}
    
    async def process_document(self, file_path: str) -> DocumentInfo:
        """
        处理Word文档，提取基础信息和结构化内容
        
        技术验证重点：
        1. python-docx的文档解析能力
        2. 异步处理大文件的性能
        3. 文本提取和清理的准确性
        4. 结构化信息的获取
        """
        logger.info(f"开始处理文档: {file_path}")
        
        try:
            # 在线程池中执行文档解析（避免阻塞事件循环）
            loop = asyncio.get_event_loop()
            document_data = await loop.run_in_executor(
                None, self._parse_document_sync, file_path
            )
            
            # 构建DocumentInfo对象
            document_info = DocumentInfo(
                filename=Path(file_path).name,
                file_size=Path(file_path).stat().st_size,
                total_paragraphs=document_data['paragraph_count'],
                total_tables=document_data['table_count'],
                total_words=document_data['word_count'],
                created_time=document_data.get('created_time'),
                modified_time=document_data.get('modified_time'),
                author=document_data.get('author')
            )
            
            logger.info(f"文档处理完成: {document_info.total_paragraphs}段落, {document_info.total_words}字")
            return document_info
            
        except Exception as e:
            logger.error(f"文档处理失败: {e}")
            raise Exception(f"文档处理失败: {str(e)}")
    
    def _parse_document_sync(self, file_path: str) -> Dict:
        """同步解析文档（在线程池中执行）"""
        try:
            # 打开Word文档
            doc = Document(file_path)
            
            # 提取文档元数据
            core_props = doc.core_properties
            
            # 解析段落内容
            paragraphs_data = []
            total_words = 0
            
            for i, paragraph in enumerate(doc.paragraphs):
                if paragraph.text.strip():  # 跳过空段落
                    # 文本清理和预处理
                    cleaned_text = self._clean_text(paragraph.text)
                    # 精确字数统计：按Word标准计算
                    word_count = self._count_words_accurately(paragraph.text)
                    
                    paragraph_info = {
                        'index': i,
                        'text': paragraph.text,
                        'cleaned_text': cleaned_text,
                        'word_count': word_count,
                        'style': paragraph.style.name if paragraph.style else None,
                        'alignment': paragraph.alignment
                    }
                    paragraphs_data.append(paragraph_info)
                    total_words += word_count
            
            # 解析表格内容
            tables_data = []
            table_words = 0
            for i, table in enumerate(doc.tables):
                table_data = self._extract_table_data(table)
                # 统计表格中的字数
                for row in table_data:
                    for cell in row:
                        table_words += self._count_words_accurately(cell)

                table_info = {
                    'index': i,
                    'rows': len(table.rows),
                    'cols': len(table.columns) if table.rows else 0,
                    'data': table_data
                }
                tables_data.append(table_info)

            # 将表格字数加入总字数
            total_words += table_words
            
            return {
                'paragraph_count': len(paragraphs_data),
                'table_count': len(tables_data),
                'word_count': total_words,
                'paragraphs': paragraphs_data,
                'tables': tables_data,
                'created_time': core_props.created,
                'modified_time': core_props.modified,
                'author': core_props.author,
                'title': core_props.title
            }
            
        except Exception as e:
            logger.error(f"同步文档解析失败: {e}")
            raise
    
    def _clean_text(self, text: str) -> str:
        """文本清理和规范化"""
        if not text:
            return ""

        # 统一编码
        text = text.encode('utf-8').decode('utf-8')

        # 清理特殊字符，保留中文、英文、数字和常用标点
        text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s\.,;:!?()（）【】""''、。，；：！？]', '', text)

        # 规范化空白字符
        text = re.sub(r'\s+', ' ', text).strip()

        return text

    def _count_words_accurately(self, text: str) -> int:
        """精确的字数统计，尽量接近Word标准"""
        if not text:
            return 0

        # 方法1：简单字符计数（不包括空格）
        # 这个方法最接近Word的"字符数(不计空格)"
        chars_no_space = len(re.sub(r'\s', '', text))

        # 方法2：Word标准计数
        # 中文字符（包括中文标点）
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))

        # 英文单词（按空格分割）
        english_text = re.sub(r'[\u4e00-\u9fff]', ' ', text)  # 移除中文
        english_words = len([word for word in english_text.split() if re.search(r'[a-zA-Z0-9]', word)])

        # 标点符号和其他字符
        punctuation = len(re.findall(r'[^\u4e00-\u9fff\sa-zA-Z0-9]', text))

        # 使用更接近Word标准的计算方式
        # 优先使用字符数(不计空格)，这通常最准确
        return chars_no_space
    
    def _extract_table_data(self, table) -> List[List[str]]:
        """提取表格数据"""
        table_data = []
        try:
            for row in table.rows:
                row_data = []
                for cell in row.cells:
                    cell_text = self._clean_text(cell.text)
                    row_data.append(cell_text)
                table_data.append(row_data)
        except Exception as e:
            logger.warning(f"表格数据提取失败: {e}")
        
        return table_data
    
    async def extract_basic_elements(self, file_path: str) -> List[ExtractedElement]:
        """
        提取基础文档要素
        
        技术验证重点：
        1. 正则表达式模式匹配的准确性
        2. 要素分类和置信度计算
        3. 位置信息的精确记录
        """
        logger.info("开始提取基础文档要素")
        
        try:
            # 在线程池中执行要素提取
            loop = asyncio.get_event_loop()
            elements = await loop.run_in_executor(
                None, self._extract_elements_sync, file_path
            )
            
            logger.info(f"基础要素提取完成: {len(elements)}个要素")
            return elements
            
        except Exception as e:
            logger.error(f"要素提取失败: {e}")
            raise
    
    def _extract_elements_sync(self, file_path: str) -> List[ExtractedElement]:
        """同步提取要素（在线程池中执行）"""
        elements = []
        
        try:
            doc = Document(file_path)
            full_text = '\n'.join([p.text for p in doc.paragraphs if p.text.strip()])
            
            # 提取合同标题
            title_element = self._extract_title(doc.paragraphs)
            if title_element:
                elements.append(title_element)
            
            # 提取当事方信息
            party_elements = self._extract_parties(full_text)
            elements.extend(party_elements)
            
            # 提取金额信息
            amount_elements = self._extract_amounts(full_text)
            elements.extend(amount_elements)
            
            # 提取日期信息
            date_elements = self._extract_dates(full_text)
            elements.extend(date_elements)
            
            return elements
            
        except Exception as e:
            logger.error(f"同步要素提取失败: {e}")
            return []
    
    def _extract_title(self, paragraphs) -> Optional[ExtractedElement]:
        """提取合同标题"""
        title_patterns = [
            r'(.{5,50}?)(合同|协议|契约|协定)',
            r'^(.{5,50})$'  # 简单的首行标题
        ]
        
        # 检查前5段
        for i, paragraph in enumerate(paragraphs[:5]):
            text = paragraph.text.strip()
            if not text or len(text) < 5:
                continue
                
            for pattern in title_patterns:
                match = re.search(pattern, text)
                if match:
                    return ExtractedElement(
                        element_name="合同标题",
                        element_category=ElementCategory.DOCUMENT_STRUCTURE,
                        value=text,
                        confidence=0.9 if '合同' in text or '协议' in text else 0.6,
                        source_position={"paragraph": i, "text": text},
                        pattern_type="title_pattern"
                    )
        
        return None
    
    def _extract_parties(self, text: str) -> List[ExtractedElement]:
        """提取当事方信息"""
        elements = []
        party_patterns = [
            (r'甲\s*方[：:]\s*(.+?)(?=\n|乙方|丙方|$)', '甲方'),
            (r'乙\s*方[：:]\s*(.+?)(?=\n|甲方|丙方|$)', '乙方'),
            (r'委托方[：:]\s*(.+?)(?=\n|受托方|$)', '委托方'),
            (r'受托方[：:]\s*(.+?)(?=\n|委托方|$)', '受托方'),
        ]
        
        for pattern, party_type in party_patterns:
            matches = re.finditer(pattern, text, re.MULTILINE | re.DOTALL)
            for match in matches:
                party_info = match.group(1).strip()
                if party_info:
                    elements.append(ExtractedElement(
                        element_name=party_type,
                        element_category=ElementCategory.PARTY_INFO,
                        value=party_info,
                        confidence=0.85,
                        source_position={"start": match.start(), "end": match.end()},
                        pattern_type="party_pattern"
                    ))
        
        return elements
    
    def _extract_amounts(self, text: str) -> List[ExtractedElement]:
        """提取金额信息"""
        elements = []
        amount_patterns = [
            r'(金额|价格|费用|总价)[：:]?\s*([0-9,，]+\.?[0-9]*)\s*(元|万元|USD|美元)',
            r'([0-9,，]+\.?[0-9]*)\s*(元|万元|USD|美元)'
        ]
        
        for pattern in amount_patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                amount_text = match.group()
                elements.append(ExtractedElement(
                    element_name="合同金额",
                    element_category=ElementCategory.FINANCIAL_TERMS,
                    value=amount_text,
                    confidence=0.8,
                    source_position={"start": match.start(), "end": match.end()},
                    pattern_type="amount_pattern"
                ))
        
        return elements
    
    def _extract_dates(self, text: str) -> List[ExtractedElement]:
        """提取日期信息"""
        elements = []
        date_patterns = [
            r'(\d{4})\s*年\s*(\d{1,2})\s*月\s*(\d{1,2})\s*日',
            r'(\d{4})[/-](\d{1,2})[/-](\d{1,2})',
            r'(签署日期|生效日期|到期日期)[：:]?\s*(.+?)(?=\n|$)'
        ]
        
        for pattern in date_patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                date_text = match.group()
                elements.append(ExtractedElement(
                    element_name="重要日期",
                    element_category=ElementCategory.TIME_TERMS,
                    value=date_text,
                    confidence=0.75,
                    source_position={"start": match.start(), "end": match.end()},
                    pattern_type="date_pattern"
                ))
        
        return elements
