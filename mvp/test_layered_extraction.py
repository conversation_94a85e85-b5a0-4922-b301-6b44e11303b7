"""
分层提取策略测试脚本
验证优化后的合同要素提取功能
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from contract_analyzer import ContractAnalyzer
from document_processor import DocumentProcessor
from models import DocumentInfo
from config_manager import config_manager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_layered_extraction():
    """测试分层提取功能"""
    logger.info("🚀 开始测试分层提取策略")
    
    # 打印配置状态
    config_manager.print_config_status()
    
    # 创建测试用的文档信息
    test_document_info = DocumentInfo(
        filename="测试销售合同.docx",
        file_size=1024 * 50,  # 50KB
        total_paragraphs=25,
        total_tables=2,
        total_words=1500,
        created_time=None,
        modified_time=None,
        author="测试用户"
    )
    
    # 初始化分析器
    analyzer = ContractAnalyzer()
    
    # 测试健康检查
    health_status = await analyzer.health_check()
    logger.info(f"分析器健康状态: {health_status}")
    
    # 测试结构化要素提取
    logger.info("\n=== 测试结构化要素提取 ===")
    try:
        structured_elements = await analyzer._extract_structured_elements(test_document_info)
        logger.info(f"✅ 结构化要素提取成功: {len(structured_elements)} 个要素")
        
        for i, element in enumerate(structured_elements[:5]):  # 显示前5个
            logger.info(f"  {i+1}. {element.element_name}: {element.value[:50]}...")
            logger.info(f"     分类: {element.element_category.value}, 置信度: {element.confidence:.2f}")
    
    except Exception as e:
        logger.error(f"❌ 结构化要素提取失败: {e}")
    
    # 测试语义要素提取（如果AI可用）
    if analyzer.qwen_client:
        logger.info("\n=== 测试语义要素提取 ===")
        try:
            # 创建模拟的合同内容
            mock_content = """
            技术开发合同
            
            甲方：北京科技有限公司
            乙方：上海软件开发公司
            
            根据《中华人民共和国合同法》等相关法律法规，甲乙双方就软件开发项目达成如下协议：
            
            一、项目内容
            乙方为甲方开发企业管理系统，包括用户管理、数据分析等功能模块。
            
            二、开发周期
            项目开发周期为6个月，自2024年1月1日起至2024年6月30日止。
            
            三、费用及支付
            项目总费用为人民币50万元，分三期支付。
            
            四、知识产权
            开发完成的软件著作权归甲方所有，乙方保留技术方案的使用权。
            
            五、违约责任
            任何一方违约，应承担违约金5万元，并赔偿对方因此造成的损失。
            
            六、争议解决
            因本合同引起的争议，双方应友好协商解决；协商不成的，提交北京仲裁委员会仲裁。
            """
            
            # 创建临时文件用于测试
            temp_file = "temp_test_contract.txt"
            with open(temp_file, 'w', encoding='utf-8') as f:
                f.write(mock_content)
            
            semantic_elements = await analyzer._extract_semantic_elements(test_document_info, temp_file)
            logger.info(f"✅ 语义要素提取成功: {len(semantic_elements)} 个要素")
            
            for i, element in enumerate(semantic_elements[:3]):  # 显示前3个
                logger.info(f"  {i+1}. {element.element_name}: {element.value[:100]}...")
                logger.info(f"     置信度: {element.confidence:.2f}")
            
            # 清理临时文件
            if os.path.exists(temp_file):
                os.remove(temp_file)
                
        except Exception as e:
            logger.error(f"❌ 语义要素提取失败: {e}")
    else:
        logger.info("⚠️  AI客户端不可用，跳过语义要素提取测试")
    
    # 测试结果合并
    logger.info("\n=== 测试结果合并 ===")
    try:
        # 模拟提取结果
        mock_results = {
            "structured": structured_elements if 'structured_elements' in locals() else [],
            "semantic": semantic_elements if 'semantic_elements' in locals() else []
        }
        
        merged_elements = await analyzer._merge_extraction_results(mock_results)
        logger.info(f"✅ 结果合并成功: {len(merged_elements)} 个要素")
        
        # 按分类统计
        category_stats = {}
        for element in merged_elements:
            category = element.element_category.value
            category_stats[category] = category_stats.get(category, 0) + 1
        
        logger.info("📊 要素分类统计:")
        for category, count in category_stats.items():
            logger.info(f"  - {category}: {count} 个")
            
    except Exception as e:
        logger.error(f"❌ 结果合并失败: {e}")
    
    logger.info("\n🎯 分层提取策略测试完成")

async def test_performance_comparison():
    """性能对比测试"""
    logger.info("\n🏃 开始性能对比测试")
    
    analyzer = ContractAnalyzer()
    
    test_document_info = DocumentInfo(
        filename="性能测试合同.docx",
        file_size=1024 * 100,  # 100KB
        total_paragraphs=50,
        total_tables=5,
        total_words=3000,
        created_time=None,
        modified_time=None,
        author="性能测试"
    )
    
    # 测试分层提取性能
    start_time = asyncio.get_event_loop().time()
    try:
        layered_elements = await analyzer._extract_contract_elements(test_document_info)
        layered_time = asyncio.get_event_loop().time() - start_time
        logger.info(f"✅ 分层提取: {len(layered_elements)} 个要素, 耗时: {layered_time:.2f}秒")
    except Exception as e:
        logger.error(f"❌ 分层提取失败: {e}")
        layered_time = 0
    
    # 测试传统提取性能
    start_time = asyncio.get_event_loop().time()
    try:
        traditional_elements = await analyzer._extract_elements_by_rules(test_document_info)
        traditional_time = asyncio.get_event_loop().time() - start_time
        logger.info(f"✅ 传统提取: {len(traditional_elements)} 个要素, 耗时: {traditional_time:.2f}秒")
    except Exception as e:
        logger.error(f"❌ 传统提取失败: {e}")
        traditional_time = 0
    
    # 性能对比
    if layered_time > 0 and traditional_time > 0:
        if layered_time < traditional_time:
            improvement = ((traditional_time - layered_time) / traditional_time) * 100
            logger.info(f"🚀 分层提取性能提升: {improvement:.1f}%")
        else:
            overhead = ((layered_time - traditional_time) / traditional_time) * 100
            logger.info(f"⚠️  分层提取性能开销: {overhead:.1f}%")

if __name__ == "__main__":
    async def main():
        await test_layered_extraction()
        await test_performance_comparison()
    
    asyncio.run(main())
