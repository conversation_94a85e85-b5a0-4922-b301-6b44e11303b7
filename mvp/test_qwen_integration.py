"""
千问3集成测试
验证千问3 API的集成和功能
"""

import pytest
import asyncio
import os
import tempfile
from pathlib import Path
from docx import Document

# 设置测试环境变量
os.environ["ENABLE_AI_ANALYSIS"] = "true"
os.environ["QWEN_API_KEY"] = "test_api_key"  # 测试用的假密钥

from qwen_api_client import QwenAPIClient
from contract_analyzer import ContractAnalyzer
from document_processor import DocumentProcessor
from config_manager import config_manager
from models import DocumentInfo

class TestQwenIntegration:
    """千问3集成测试"""
    
    @pytest.fixture
    def sample_contract_content(self):
        """创建测试合同内容"""
        return """
技术开发合同

甲方：北京科技有限公司
地址：北京市海淀区中关村大街1号
联系电话：010-12345678

乙方：上海软件开发有限公司
地址：上海市浦东新区张江高科技园区
联系电话：021-87654321

合同金额：人民币500,000元整

项目内容：开发企业管理系统
开发周期：6个月
交付日期：2024年12月31日

付款方式：
1. 签约后支付30%，即150,000元
2. 项目完成50%后支付40%，即200,000元
3. 项目验收合格后支付剩余30%，即150,000元

违约责任：
如一方违约，应向对方支付合同总金额10%的违约金。

争议解决：
本合同争议由北京市海淀区人民法院管辖。

签署日期：2024年6月1日
        """
    
    @pytest.fixture
    def sample_docx_file(self, sample_contract_content):
        """创建测试Word文档"""
        doc = Document()
        doc.add_heading('技术开发合同', 0)
        
        # 添加合同内容
        for line in sample_contract_content.strip().split('\n'):
            if line.strip():
                doc.add_paragraph(line.strip())
        
        # 保存到临时文件
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.docx')
        doc.save(temp_file.name)
        
        yield temp_file.name
        
        # 清理
        os.unlink(temp_file.name)
    
    def test_config_manager_initialization(self):
        """测试配置管理器初始化"""
        assert config_manager is not None
        
        # 测试配置获取
        qwen_config = config_manager.get_qwen_config()
        assert "api_key" in qwen_config
        assert "base_url" in qwen_config
        assert "model_name" in qwen_config
    
    @pytest.mark.asyncio
    async def test_qwen_client_initialization(self):
        """测试千问3客户端初始化"""
        client = QwenAPIClient(api_key="test_key")
        assert client.api_key == "test_key"
        assert client.model_name == "qwen-max"
        assert client.timeout == 60
        
        await client.client.aclose()
    
    def test_contract_analyzer_initialization(self):
        """测试合同分析器初始化"""
        analyzer = ContractAnalyzer()
        assert analyzer.document_processor is not None
        assert analyzer.config is not None
        assert analyzer.qwen_config is not None
        
        # 检查千问3客户端是否正确初始化
        # 注意：在测试环境中，由于API密钥是假的，客户端可能不会初始化
        if config_manager.is_ai_enabled():
            assert analyzer.qwen_client is not None
    
    @pytest.mark.asyncio
    async def test_document_content_reading(self, sample_docx_file):
        """测试文档内容读取"""
        analyzer = ContractAnalyzer()
        
        content = await analyzer._read_document_content(sample_docx_file)
        
        assert content is not None
        assert len(content) > 0
        assert "技术开发合同" in content
        assert "甲方" in content
        assert "乙方" in content
        assert "500,000" in content
    
    @pytest.mark.asyncio
    async def test_contract_type_identification_fallback(self, sample_docx_file):
        """测试合同类型识别（降级模式）"""
        analyzer = ContractAnalyzer()
        
        document_info = DocumentInfo(
            filename="技术开发合同.docx",
            file_size=1024,
            total_paragraphs=20,
            total_tables=0,
            total_words=500
        )
        
        contract_type = await analyzer._identify_contract_type(document_info, sample_docx_file)
        
        # 由于API密钥是假的，应该降级到规则匹配
        # "技术开发"应该被识别为service类型
        assert contract_type.value in ["service", "general"]
    
    @pytest.mark.asyncio
    async def test_element_extraction_fallback(self, sample_docx_file):
        """测试要素提取（降级模式）"""
        analyzer = ContractAnalyzer()
        
        document_info = DocumentInfo(
            filename="技术开发合同.docx",
            file_size=1024,
            total_paragraphs=20,
            total_tables=0,
            total_words=500
        )
        
        elements = await analyzer._extract_contract_elements(document_info, sample_docx_file)
        
        # 应该至少提取到一些基础要素
        assert len(elements) > 0
        
        # 检查要素类型
        element_names = [elem.element_name for elem in elements]
        assert any("合同" in name for name in element_names)
    
    @pytest.mark.asyncio
    async def test_full_contract_analysis(self, sample_docx_file):
        """测试完整合同分析流程"""
        # 创建文档处理器和分析器
        processor = DocumentProcessor()
        analyzer = ContractAnalyzer()
        
        # 处理文档
        document_info = await processor.process_document(sample_docx_file)
        document_info.filename = "技术开发合同.docx"
        
        # 分析合同
        result = await analyzer.analyze_contract(
            document_info=document_info,
            original_filename="技术开发合同.docx",
            temp_file_path=sample_docx_file
        )
        
        # 验证分析结果
        assert result is not None
        assert result.contract_type is not None
        assert result.processing_duration > 0
        assert 0 <= result.overall_confidence <= 1
        assert 0 <= result.completeness_score <= 1
        assert 0 <= result.risk_score <= 1
        
        # 验证统计信息
        assert "total_elements" in result.statistics
        assert "total_missing_clauses" in result.statistics
        assert "total_risk_points" in result.statistics
    
    def test_qwen_response_parsing(self):
        """测试千问3响应解析"""
        client = QwenAPIClient(api_key="test_key")
        
        # 测试合同类型响应解析
        response = '{"contract_type": "service", "confidence": 0.95, "reasoning": "技术开发合同"}'
        result = client._parse_contract_type_response(response)
        
        assert result["contract_type"] == "service"
        assert result["confidence"] == 0.95
        
        # 测试文本解析降级
        response = "这是一个服务合同，涉及技术开发"
        result = client._parse_contract_type_response(response)
        
        assert result["contract_type"] == "service"
        assert result["confidence"] == 0.7
    
    @pytest.mark.asyncio
    async def test_error_handling(self):
        """测试错误处理"""
        analyzer = ContractAnalyzer()
        
        # 测试不存在的文件
        content = await analyzer._read_document_content("nonexistent_file.docx")
        assert content == ""
        
        # 测试无效的文档信息
        document_info = DocumentInfo(
            filename="test.docx",
            file_size=0,
            total_paragraphs=0,
            total_tables=0,
            total_words=0
        )
        
        contract_type = await analyzer._identify_contract_type(document_info, "nonexistent_file.docx")
        assert contract_type is not None  # 应该降级到默认类型
    
    def test_configuration_validation(self):
        """测试配置验证"""
        # 测试AI启用状态
        ai_enabled = config_manager.is_ai_enabled()
        assert isinstance(ai_enabled, bool)
        
        # 测试配置获取
        file_config = config_manager.get_file_config()
        assert "max_file_size" in file_config
        assert "supported_formats" in file_config
        
        analysis_config = config_manager.get_analysis_config()
        assert "enable_ai_analysis" in analysis_config
        assert "fallback_to_rules" in analysis_config

if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v", "--tb=short"])
