"""
合同分析器 - 核心业务逻辑
集成AI API调用、风险识别、缺失条款检测等核心功能
"""

import asyncio
import logging
import time
import os
from typing import Dict, List, Optional
import json
import httpx
from datetime import datetime

from models import (
    DocumentInfo, ContractAnalysisResult, ContractType,
    ExtractedElement, MissingClause, RiskPoint, RiskLevel,
    ElementCategory
)
from document_processor import DocumentProcessor
from qwen_api_client import QwenAPIClient
from config_manager import config_manager

logger = logging.getLogger(__name__)

class ContractAnalyzer:
    """合同分析器 - 负责合同的智能分析和风险识别"""
    
    def __init__(self):
        self.document_processor = DocumentProcessor()

        # 获取配置
        self.config = config_manager.get_analysis_config()
        self.qwen_config = config_manager.get_qwen_config()

        # 初始化千问3客户端
        self.qwen_client = None
        if config_manager.is_ai_enabled():
            self.qwen_client = QwenAPIClient(
                api_key=self.qwen_config["api_key"],
                base_url=self.qwen_config["base_url"]
            )
            logger.info("千问3 API客户端已初始化")
        else:
            logger.info("AI分析未启用，将使用规则匹配模式")
        
        # 合同类型识别规则
        self.contract_type_rules = {
            ContractType.SALES: ["销售", "买卖", "购买", "采购", "商品", "产品"],
            ContractType.SERVICE: ["服务", "咨询", "技术支持", "维护", "开发", "技术开发", "软件开发", "系统开发"],
            ContractType.LEASE: ["租赁", "出租", "承租", "租金", "房屋", "场地"],
            ContractType.EMPLOYMENT: ["劳动", "雇佣", "员工", "工资", "职位", "聘用"],
            ContractType.GENERAL: ["合同", "协议", "约定"]
        }
        
        # 必要条款检查规则
        self.required_clauses = {
            ContractType.SALES: [
                "商品描述", "价格条款", "交付条款", "付款方式", "违约责任"
            ],
            ContractType.SERVICE: [
                "服务内容", "服务标准", "服务期限", "费用支付", "知识产权"
            ],
            ContractType.LEASE: [
                "租赁物描述", "租赁期限", "租金标准", "押金条款", "维修责任"
            ],
            ContractType.EMPLOYMENT: [
                "工作内容", "工作地点", "工作时间", "薪酬福利", "保密条款"
            ],
            ContractType.GENERAL: [
                "合同主体", "权利义务", "履行期限", "违约责任", "争议解决"
            ]
        }
    
    async def health_check(self) -> Dict[str, str]:
        """健康检查"""
        try:
            # 检查千问3客户端状态
            if self.qwen_client:
                api_status = True
                status_msg = "ai_enabled"
            else:
                api_status = False
                status_msg = "rules_only"

            return {
                "status": "healthy",
                "component": "contract_analyzer",
                "mode": status_msg,
                "ai_client": "available" if self.qwen_client else "unavailable"
            }
        except Exception as e:
            logger.error(f"合同分析器健康检查失败: {e}")
            return {"status": "unhealthy", "error": str(e)}
    
    async def analyze_contract(
        self,
        document_info: DocumentInfo,
        original_filename: str,
        temp_file_path: str = None
    ) -> ContractAnalysisResult:
        """
        执行完整的合同分析
        
        技术验证重点：
        1. 异步AI API调用的性能和稳定性
        2. 多任务并发处理能力
        3. 错误处理和降级策略
        4. 结果整合和质量评估
        """
        start_time = time.time()
        logger.info(f"开始分析合同: {original_filename}")
        
        try:
            # 步骤1: 合同类型识别
            contract_type = await self._identify_contract_type(document_info, temp_file_path)
            logger.info(f"合同类型识别: {contract_type}")
            
            # 步骤2: 并发执行多个分析任务
            tasks = [
                self._extract_contract_elements(document_info, temp_file_path),
                self._detect_missing_clauses(document_info, contract_type),
                self._identify_risk_points(document_info, contract_type)
            ]
            
            elements, missing_clauses, risk_points = await asyncio.gather(*tasks)
            
            # 步骤3: 计算统计信息和质量评分
            statistics = self._calculate_statistics(elements, missing_clauses, risk_points)
            overall_confidence = self._calculate_overall_confidence(elements)
            completeness_score = self._calculate_completeness_score(missing_clauses, contract_type)
            risk_score = self._calculate_risk_score(risk_points)
            
            # 步骤4: 按分类组织要素
            elements_by_category = self._group_elements_by_category(elements)
            
            processing_duration = time.time() - start_time
            
            # 构建分析结果
            result = ContractAnalysisResult(
                document_info=document_info,
                contract_type=contract_type,
                processing_duration=processing_duration,
                extracted_elements=elements,
                elements_by_category=elements_by_category,
                missing_clauses=missing_clauses,
                risk_points=risk_points,
                statistics=statistics,
                overall_confidence=overall_confidence,
                completeness_score=completeness_score,
                risk_score=risk_score
            )
            
            logger.info(f"合同分析完成: 耗时{processing_duration:.2f}秒, 置信度{overall_confidence:.2f}")
            return result
            
        except Exception as e:
            logger.error(f"合同分析失败: {e}")
            raise Exception(f"合同分析失败: {str(e)}")
    
    async def _identify_contract_type(self, document_info: DocumentInfo, temp_file_path: str = None) -> ContractType:
        """识别合同类型"""
        try:
            # 如果启用AI分析且有千问3客户端
            if self.qwen_client and temp_file_path and os.path.exists(temp_file_path):
                logger.info("🤖 使用千问3进行合同类型识别")

                # 读取文档内容
                document_content = await self._read_document_content(temp_file_path)
                logger.info(f"📖 已读取文档内容: {len(document_content)} 字符")

                # 调用千问3进行合同类型识别
                ai_result = await self.qwen_client.analyze_contract_type(
                    document_content=document_content,
                    filename=document_info.filename
                )

                if ai_result and "contract_type" in ai_result:
                    contract_type = ai_result['contract_type']
                    confidence = ai_result.get('confidence', 0)
                    reasoning = ai_result.get('reasoning', '无')

                    logger.info(f"🎯 千问3识别结果:")
                    logger.info(f"   合同类型: {contract_type}")
                    logger.info(f"   置信度: {confidence:.2f}")
                    logger.info(f"   推理过程: {reasoning}")

                    return ContractType(contract_type)

            # 降级到规则匹配
            logger.info("使用规则匹配识别合同类型")
            return self._classify_by_rules(document_info.filename)

        except Exception as e:
            logger.warning(f"AI合同类型识别失败，使用规则匹配: {e}")
            return self._classify_by_rules(document_info.filename)
    
    def _classify_by_rules(self, filename: str) -> ContractType:
        """基于规则的合同类型分类"""
        filename_lower = filename.lower()

        # 优先匹配更具体的类型
        type_priority = [
            ContractType.SALES,
            ContractType.SERVICE,
            ContractType.LEASE,
            ContractType.EMPLOYMENT
        ]

        for contract_type in type_priority:
            keywords = self.contract_type_rules.get(contract_type, [])
            if any(keyword in filename_lower for keyword in keywords):
                return contract_type

        return ContractType.GENERAL
    
    async def _extract_contract_elements(self, document_info: DocumentInfo, temp_file_path: str = None) -> List[ExtractedElement]:
        """提取合同要素"""
        try:
            # 如果启用AI分析且有千问3客户端
            if self.qwen_client and temp_file_path and os.path.exists(temp_file_path):
                logger.info("🤖 使用千问3进行要素提取")

                # 读取文档内容
                document_content = await self._read_document_content(temp_file_path)
                logger.info(f"📖 已读取文档内容: {len(document_content)} 字符")

                # 调用千问3进行要素提取
                ai_result = await self.qwen_client.extract_contract_elements(
                    document_content=document_content,
                    contract_type="general"  # 可以传入已识别的类型
                )

                if ai_result and "elements" in ai_result:
                    elements = []
                    logger.info(f"🔍 千问3返回了 {len(ai_result['elements'])} 个要素")

                    for i, elem_data in enumerate(ai_result["elements"]):
                        try:
                            element = ExtractedElement(
                                element_name=elem_data.get("element_name", "未知要素"),
                                element_category=ElementCategory(elem_data.get("category", "contract_terms")),
                                value=elem_data.get("value", ""),
                                confidence=elem_data.get("confidence", 0.8),
                                source_position={"ai_extracted": True, "location": elem_data.get("source_location", "")},
                                pattern_type="ai_extraction"
                            )
                            elements.append(element)
                            logger.info(f"  ✅ 要素 {i+1}: {element.element_name} = {element.value[:50]}...")
                        except Exception as e:
                            logger.warning(f"❌ 解析AI要素失败: {e}")

                    if elements:
                        logger.info(f"🎯 千问3成功提取 {len(elements)} 个有效要素")
                        return elements

            # 降级到基础规则提取
            logger.info("使用规则匹配提取要素")
            return await self._extract_elements_by_rules(document_info, temp_file_path)

        except Exception as e:
            logger.warning(f"要素提取失败，使用基础规则: {e}")
            return await self._extract_elements_by_rules(document_info, temp_file_path)
    
    async def _extract_elements_by_rules(self, document_info: DocumentInfo, temp_file_path: str = None) -> List[ExtractedElement]:
        """基于规则的要素提取（降级方案）"""

        # 如果临时文件不存在，创建基础要素
        elements = [
            ExtractedElement(
                element_name="合同标题",
                element_category=ElementCategory.DOCUMENT_STRUCTURE,
                value=document_info.filename.replace('.docx', '').replace('.doc', ''),
                confidence=0.8,
                source_position={"paragraph": 0},
                pattern_type="filename_extraction"
            ),
            ExtractedElement(
                element_name="文档统计",
                element_category=ElementCategory.DOCUMENT_STRUCTURE,
                value=f"共{document_info.total_paragraphs}段落，{document_info.total_words}字",
                confidence=1.0,
                source_position={"type": "document_stats"},
                pattern_type="statistics"
            )
        ]

        # 尝试从文档处理器获取更多要素
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                processor_elements = await self.document_processor.extract_basic_elements(temp_file_path)
                elements.extend(processor_elements)
                logger.info(f"成功提取{len(processor_elements)}个要素")
            except Exception as e:
                logger.warning(f"文档处理器要素提取失败: {e}")
        else:
            logger.warning(f"临时文件不存在: {temp_file_path}")

        # 添加一些基于文档统计的推断要素
        if document_info.total_words > 500:
            elements.append(ExtractedElement(
                element_name="合同性质",
                element_category=ElementCategory.CONTRACT_TERMS,
                value="标准商务合同",
                confidence=0.7,
                source_position={"type": "inferred"},
                pattern_type="content_analysis"
            ))

        if document_info.total_paragraphs > 50:
            elements.append(ExtractedElement(
                element_name="合同复杂度",
                element_category=ElementCategory.DOCUMENT_STRUCTURE,
                value="中等复杂度合同",
                confidence=0.8,
                source_position={"type": "statistical"},
                pattern_type="complexity_analysis"
            ))

        # 基于文件名推断要素
        filename_lower = document_info.filename.lower()
        if any(keyword in filename_lower for keyword in ['销售', '买卖', 'sales']):
            elements.append(ExtractedElement(
                element_name="合同类型推断",
                element_category=ElementCategory.CONTRACT_TERMS,
                value="销售类合同",
                confidence=0.8,
                source_position={"type": "filename"},
                pattern_type="filename_analysis"
            ))
        elif any(keyword in filename_lower for keyword in ['服务', 'service']):
            elements.append(ExtractedElement(
                element_name="合同类型推断",
                element_category=ElementCategory.CONTRACT_TERMS,
                value="服务类合同",
                confidence=0.8,
                source_position={"type": "filename"},
                pattern_type="filename_analysis"
            ))

        return elements
    
    async def _detect_missing_clauses(
        self,
        document_info: DocumentInfo,
        contract_type: ContractType
    ) -> List[MissingClause]:
        """检测缺失条款"""
        try:
            # 如果启用AI分析且有千问3客户端
            if self.qwen_client:
                # 这里可以调用千问3进行缺失条款检测
                # 暂时直接降级到规则检测
                logger.info("千问3缺失条款检测功能待实现，使用规则检测")

            # 降级到规则检测
            return self._detect_missing_by_rules(contract_type)

        except Exception as e:
            logger.warning(f"AI缺失条款检测失败，使用规则检测: {e}")
            return self._detect_missing_by_rules(contract_type)
    
    def _detect_missing_by_rules(self, contract_type: ContractType) -> List[MissingClause]:
        """基于规则的缺失条款检测"""
        required_clauses = self.required_clauses.get(contract_type, [])
        
        # 模拟检测结果 - 假设缺失部分条款
        missing_clauses = []
        for i, clause_name in enumerate(required_clauses[:2]):  # 模拟缺失前2个条款
            missing_clauses.append(MissingClause(
                clause_name=clause_name,
                clause_type="必要条款",
                importance_level=RiskLevel.MEDIUM,
                description=f"合同中缺少{clause_name}相关条款",
                recommendation=f"建议补充{clause_name}的具体内容和标准"
            ))
        
        return missing_clauses
    
    async def _identify_risk_points(
        self, 
        document_info: DocumentInfo, 
        contract_type: ContractType
    ) -> List[RiskPoint]:
        """识别风险点"""
        try:
            # 如果启用AI分析且有千问3客户端
            if self.qwen_client:
                # 这里可以调用千问3进行风险识别
                # 暂时直接降级到规则识别
                logger.info("千问3风险识别功能待实现，使用规则识别")

            # 降级到规则识别
            return self._identify_risks_by_rules(document_info, contract_type)

        except Exception as e:
            logger.warning(f"AI风险识别失败，使用规则识别: {e}")
            return self._identify_risks_by_rules(document_info, contract_type)
    
    def _identify_risks_by_rules(
        self, 
        document_info: DocumentInfo, 
        contract_type: ContractType
    ) -> List[RiskPoint]:
        """基于规则的风险识别"""
        risk_points = []
        
        # 模拟风险点识别
        if document_info.total_words < 500:
            risk_points.append(RiskPoint(
                risk_id="R001",
                risk_type="文档完整性风险",
                risk_level=RiskLevel.MEDIUM,
                description="合同内容过于简短，可能存在条款不完整的风险",
                location="整体文档",
                suggestion="建议补充详细的合同条款和约定事项",
                related_clause="整体结构"
            ))
        
        if not document_info.author:
            risk_points.append(RiskPoint(
                risk_id="R002",
                risk_type="文档来源风险",
                risk_level=RiskLevel.LOW,
                description="文档缺少作者信息，无法确认起草方",
                location="文档属性",
                suggestion="建议确认文档的起草方和版本信息",
                related_clause="文档元数据"
            ))
        
        return risk_points
    

    
    def _calculate_statistics(
        self, 
        elements: List[ExtractedElement], 
        missing_clauses: List[MissingClause], 
        risk_points: List[RiskPoint]
    ) -> Dict:
        """计算统计信息"""
        return {
            "total_elements": len(elements),
            "total_missing_clauses": len(missing_clauses),
            "total_risk_points": len(risk_points),
            "high_risk_count": len([r for r in risk_points if r.risk_level == RiskLevel.HIGH]),
            "medium_risk_count": len([r for r in risk_points if r.risk_level == RiskLevel.MEDIUM]),
            "low_risk_count": len([r for r in risk_points if r.risk_level == RiskLevel.LOW])
        }
    
    def _calculate_overall_confidence(self, elements: List[ExtractedElement]) -> float:
        """计算整体置信度"""
        if not elements:
            return 0.5  # 基础置信度

        # 计算加权平均置信度
        total_confidence = sum(elem.confidence for elem in elements)
        avg_confidence = total_confidence / len(elements)

        # 基于要素数量调整置信度
        element_bonus = min(len(elements) * 0.1, 0.3)  # 最多30%的奖励

        return min(avg_confidence + element_bonus, 1.0)
    
    def _calculate_completeness_score(
        self, 
        missing_clauses: List[MissingClause], 
        contract_type: ContractType
    ) -> float:
        """计算完整性评分"""
        required_count = len(self.required_clauses.get(contract_type, []))
        if required_count == 0:
            return 1.0
        
        missing_count = len(missing_clauses)
        return max(0.0, (required_count - missing_count) / required_count)
    
    def _calculate_risk_score(self, risk_points: List[RiskPoint]) -> float:
        """计算风险评分"""
        if not risk_points:
            return 0.0
        
        risk_weights = {RiskLevel.HIGH: 1.0, RiskLevel.MEDIUM: 0.6, RiskLevel.LOW: 0.3}
        total_risk = sum(risk_weights.get(risk.risk_level, 0.5) for risk in risk_points)
        
        # 归一化到0-1范围
        return min(total_risk / len(risk_points), 1.0)
    
    def _group_elements_by_category(
        self, 
        elements: List[ExtractedElement]
    ) -> Dict[str, List[ExtractedElement]]:
        """按分类组织要素"""
        grouped = {}
        for element in elements:
            category = element.element_category.value
            if category not in grouped:
                grouped[category] = []
            grouped[category].append(element)
        
        return grouped

    async def _read_document_content(self, file_path: str) -> str:
        """读取文档内容用于AI分析"""
        try:
            from docx import Document

            doc = Document(file_path)
            content_parts = []

            # 提取段落内容
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    content_parts.append(paragraph.text.strip())

            # 提取表格内容
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text.strip())
                    if row_text:
                        content_parts.append(" | ".join(row_text))

            full_content = "\n".join(content_parts)

            # 限制内容长度，避免超出API限制
            max_length = self.config.get("max_content_length", 10000)
            if len(full_content) > max_length:
                full_content = full_content[:max_length] + "\n...(内容已截断)"

            return full_content

        except Exception as e:
            logger.error(f"读取文档内容失败: {e}")
            return ""
