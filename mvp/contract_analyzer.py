"""
合同分析器 - 核心业务逻辑
集成AI API调用、风险识别、缺失条款检测等核心功能
"""

import asyncio
import logging
import time
import os
from typing import Dict, List, Optional
import json
import httpx
from datetime import datetime

from models import (
    DocumentInfo, ContractAnalysisResult, ContractType,
    ExtractedElement, MissingClause, RiskPoint, RiskLevel,
    ElementCategory
)
from document_processor import DocumentProcessor
from qwen_api_client import QwenAPIClient
from config_manager import config_manager

logger = logging.getLogger(__name__)

class ContractAnalyzer:
    """合同分析器 - 负责合同的智能分析和风险识别"""
    
    def __init__(self):
        self.document_processor = DocumentProcessor()

        # 获取配置
        self.config = config_manager.get_analysis_config()
        self.qwen_config = config_manager.get_qwen_config()

        # 初始化千问3客户端
        self.qwen_client = None
        if config_manager.is_ai_enabled():
            self.qwen_client = QwenAPIClient(
                api_key=self.qwen_config["api_key"],
                base_url=self.qwen_config["base_url"]
            )
            logger.info("千问3 API客户端已初始化")
        else:
            logger.info("AI分析未启用，将使用规则匹配模式")
        
        # 合同类型识别规则
        self.contract_type_rules = {
            ContractType.SALES: ["销售", "买卖", "购买", "采购", "商品", "产品"],
            ContractType.SERVICE: ["服务", "咨询", "技术支持", "维护", "开发", "技术开发", "软件开发", "系统开发"],
            ContractType.LEASE: ["租赁", "出租", "承租", "租金", "房屋", "场地"],
            ContractType.EMPLOYMENT: ["劳动", "雇佣", "员工", "工资", "职位", "聘用"],
            ContractType.GENERAL: ["合同", "协议", "约定"]
        }
        
        # 必要条款检查规则
        self.required_clauses = {
            ContractType.SALES: [
                "商品描述", "价格条款", "交付条款", "付款方式", "违约责任"
            ],
            ContractType.SERVICE: [
                "服务内容", "服务标准", "服务期限", "费用支付", "知识产权"
            ],
            ContractType.LEASE: [
                "租赁物描述", "租赁期限", "租金标准", "押金条款", "维修责任"
            ],
            ContractType.EMPLOYMENT: [
                "工作内容", "工作地点", "工作时间", "薪酬福利", "保密条款"
            ],
            ContractType.GENERAL: [
                "合同主体", "权利义务", "履行期限", "违约责任", "争议解决"
            ]
        }
    
    async def health_check(self) -> Dict[str, str]:
        """健康检查"""
        try:
            # 检查千问3客户端状态
            if self.qwen_client:
                api_status = True
                status_msg = "ai_enabled"
            else:
                api_status = False
                status_msg = "rules_only"

            return {
                "status": "healthy",
                "component": "contract_analyzer",
                "mode": status_msg,
                "ai_client": "available" if self.qwen_client else "unavailable"
            }
        except Exception as e:
            logger.error(f"合同分析器健康检查失败: {e}")
            return {"status": "unhealthy", "error": str(e)}
    
    async def analyze_contract(
        self,
        document_info: DocumentInfo,
        original_filename: str,
        temp_file_path: str = None
    ) -> ContractAnalysisResult:
        """
        执行完整的合同分析
        
        技术验证重点：
        1. 异步AI API调用的性能和稳定性
        2. 多任务并发处理能力
        3. 错误处理和降级策略
        4. 结果整合和质量评估
        """
        start_time = time.time()
        logger.info(f"开始分析合同: {original_filename}")
        
        try:
            # 步骤1: 合同类型识别
            contract_type = await self._identify_contract_type(document_info, temp_file_path)
            logger.info(f"合同类型识别: {contract_type}")
            
            # 步骤2: 并发执行多个分析任务
            tasks = [
                self._extract_contract_elements(document_info, temp_file_path),
                self._detect_missing_clauses(document_info, contract_type),
                self._identify_risk_points(document_info, contract_type)
            ]
            
            elements, missing_clauses, risk_points = await asyncio.gather(*tasks)
            
            # 步骤3: 计算统计信息和质量评分
            statistics = self._calculate_statistics(elements, missing_clauses, risk_points)
            overall_confidence = self._calculate_overall_confidence(elements)
            completeness_score = self._calculate_completeness_score(missing_clauses, contract_type)
            risk_score = self._calculate_risk_score(risk_points)
            
            # 步骤4: 按分类组织要素
            elements_by_category = self._group_elements_by_category(elements)
            
            processing_duration = time.time() - start_time
            
            # 构建分析结果
            result = ContractAnalysisResult(
                document_info=document_info,
                contract_type=contract_type,
                processing_duration=processing_duration,
                extracted_elements=elements,
                elements_by_category=elements_by_category,
                missing_clauses=missing_clauses,
                risk_points=risk_points,
                statistics=statistics,
                overall_confidence=overall_confidence,
                completeness_score=completeness_score,
                risk_score=risk_score
            )
            
            logger.info(f"合同分析完成: 耗时{processing_duration:.2f}秒, 置信度{overall_confidence:.2f}")
            return result
            
        except Exception as e:
            logger.error(f"合同分析失败: {e}")
            raise Exception(f"合同分析失败: {str(e)}")
    
    async def _identify_contract_type(self, document_info: DocumentInfo, temp_file_path: str = None) -> ContractType:
        """识别合同类型"""
        try:
            # 如果启用AI分析且有千问3客户端
            if self.qwen_client and temp_file_path and os.path.exists(temp_file_path):
                logger.info("🤖 使用千问3进行合同类型识别")

                # 读取文档内容
                document_content = await self._read_document_content(temp_file_path)
                logger.info(f"📖 已读取文档内容: {len(document_content)} 字符")

                # 调用千问3进行合同类型识别
                ai_result = await self.qwen_client.analyze_contract_type(
                    document_content=document_content,
                    filename=document_info.filename
                )

                if ai_result and "contract_type" in ai_result:
                    contract_type = ai_result['contract_type']
                    confidence = ai_result.get('confidence', 0)
                    reasoning = ai_result.get('reasoning', '无')

                    logger.info(f"🎯 千问3识别结果:")
                    logger.info(f"   合同类型: {contract_type}")
                    logger.info(f"   置信度: {confidence:.2f}")
                    logger.info(f"   推理过程: {reasoning}")

                    return ContractType(contract_type)

            # 降级到规则匹配
            logger.info("使用规则匹配识别合同类型")
            return self._classify_by_rules(document_info.filename)

        except Exception as e:
            logger.warning(f"AI合同类型识别失败，使用规则匹配: {e}")
            return self._classify_by_rules(document_info.filename)
    
    def _classify_by_rules(self, filename: str) -> ContractType:
        """基于规则的合同类型分类"""
        filename_lower = filename.lower()

        # 优先匹配更具体的类型
        type_priority = [
            ContractType.SALES,
            ContractType.SERVICE,
            ContractType.LEASE,
            ContractType.EMPLOYMENT
        ]

        for contract_type in type_priority:
            keywords = self.contract_type_rules.get(contract_type, [])
            if any(keyword in filename_lower for keyword in keywords):
                return contract_type

        return ContractType.GENERAL
    
    async def _extract_contract_elements(self, document_info: DocumentInfo, temp_file_path: str = None) -> List[ExtractedElement]:
        """
        双重验证提取合同要素 - 真正的双重验证策略

        策略：
        1. 并行执行正则提取和AI提取（都处理所有要素）
        2. 对比两种方法的结果，相互验证和补充
        3. 智能合并，解决冲突，提高准确性
        """
        try:
            logger.info("🚀 开始双重验证提取合同要素")

            # 并行执行正则提取和AI提取
            tasks = []

            # 任务1: 正则提取（处理所有要素）
            regex_task = asyncio.create_task(
                self._extract_by_regex_comprehensive(document_info, temp_file_path)
            )
            tasks.append(("regex", regex_task))

            # 任务2: AI提取（处理所有要素）- 仅在AI可用时
            if self.qwen_client and temp_file_path and os.path.exists(temp_file_path):
                ai_task = asyncio.create_task(
                    self._extract_by_ai_comprehensive(document_info, temp_file_path)
                )
                tasks.append(("ai", ai_task))

            # 等待所有任务完成
            results = {}
            for task_name, task in tasks:
                try:
                    result = await task
                    results[task_name] = result
                    logger.info(f"✅ {task_name} 提取完成: {len(result)} 个要素")
                except Exception as e:
                    logger.warning(f"❌ {task_name} 提取失败: {e}")
                    results[task_name] = []

            # 双重验证合并结果
            merged_elements = await self._dual_verification_merge(results)

            logger.info(f"🎯 双重验证提取完成: 总计 {len(merged_elements)} 个要素")
            return merged_elements

        except Exception as e:
            logger.error(f"双重验证提取失败，降级到基础规则: {e}")
            return await self._extract_elements_by_rules(document_info, temp_file_path)
    
    async def _extract_elements_by_rules(self, document_info: DocumentInfo, temp_file_path: str = None) -> List[ExtractedElement]:
        """基于规则的要素提取（降级方案）"""

        # 如果临时文件不存在，创建基础要素
        elements = [
            ExtractedElement(
                element_name="合同标题",
                element_category=ElementCategory.DOCUMENT_STRUCTURE,
                value=document_info.filename.replace('.docx', '').replace('.doc', ''),
                confidence=0.8,
                source_position={"paragraph": 0},
                pattern_type="filename_extraction"
            ),
            ExtractedElement(
                element_name="文档统计",
                element_category=ElementCategory.DOCUMENT_STRUCTURE,
                value=f"共{document_info.total_paragraphs}段落，{document_info.total_words}字",
                confidence=1.0,
                source_position={"type": "document_stats"},
                pattern_type="statistics"
            )
        ]

        # 尝试从文档处理器获取更多要素
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                processor_elements = await self.document_processor.extract_basic_elements(temp_file_path)
                elements.extend(processor_elements)
                logger.info(f"成功提取{len(processor_elements)}个要素")
            except Exception as e:
                logger.warning(f"文档处理器要素提取失败: {e}")
        else:
            logger.warning(f"临时文件不存在: {temp_file_path}")

        # 添加一些基于文档统计的推断要素
        if document_info.total_words > 500:
            elements.append(ExtractedElement(
                element_name="合同性质",
                element_category=ElementCategory.CONTRACT_TERMS,
                value="标准商务合同",
                confidence=0.7,
                source_position={"type": "inferred"},
                pattern_type="content_analysis"
            ))

        if document_info.total_paragraphs > 50:
            elements.append(ExtractedElement(
                element_name="合同复杂度",
                element_category=ElementCategory.DOCUMENT_STRUCTURE,
                value="中等复杂度合同",
                confidence=0.8,
                source_position={"type": "statistical"},
                pattern_type="complexity_analysis"
            ))

        # 基于文件名推断要素
        filename_lower = document_info.filename.lower()
        if any(keyword in filename_lower for keyword in ['销售', '买卖', 'sales']):
            elements.append(ExtractedElement(
                element_name="合同类型推断",
                element_category=ElementCategory.CONTRACT_TERMS,
                value="销售类合同",
                confidence=0.8,
                source_position={"type": "filename"},
                pattern_type="filename_analysis"
            ))
        elif any(keyword in filename_lower for keyword in ['服务', 'service']):
            elements.append(ExtractedElement(
                element_name="合同类型推断",
                element_category=ElementCategory.CONTRACT_TERMS,
                value="服务类合同",
                confidence=0.8,
                source_position={"type": "filename"},
                pattern_type="filename_analysis"
            ))

        return elements

    async def _extract_structured_elements(self, document_info: DocumentInfo, temp_file_path: str = None) -> List[ExtractedElement]:
        """
        提取结构化要素（正则匹配）

        专门处理格式化程度高的要素：
        - 金额信息
        - 日期信息
        - 当事方信息
        - 联系方式
        - 文档结构信息
        """
        logger.info("📋 开始结构化要素提取")
        elements = []

        try:
            # 基础文档信息
            elements.extend(self._extract_document_structure_elements(document_info))

            # 从文档处理器获取正则提取的要素
            if temp_file_path and os.path.exists(temp_file_path):
                processor_elements = await self.document_processor.extract_basic_elements(temp_file_path)

                # 过滤出结构化要素
                structured_categories = {
                    ElementCategory.FINANCIAL_TERMS,
                    ElementCategory.TIME_TERMS,
                    ElementCategory.PARTY_INFO,
                    ElementCategory.DOCUMENT_STRUCTURE
                }

                filtered_elements = [
                    elem for elem in processor_elements
                    if elem.element_category in structured_categories
                ]

                elements.extend(filtered_elements)
                logger.info(f"📊 从文档处理器获取 {len(filtered_elements)} 个结构化要素")

            # 基于文件名的推断要素
            elements.extend(self._extract_filename_based_elements(document_info))

            logger.info(f"✅ 结构化要素提取完成: {len(elements)} 个要素")
            return elements

        except Exception as e:
            logger.error(f"结构化要素提取失败: {e}")
            return elements

    def _extract_document_structure_elements(self, document_info: DocumentInfo) -> List[ExtractedElement]:
        """提取文档结构要素"""
        elements = []

        # 合同标题（基于文件名）
        title_value = document_info.filename.replace('.docx', '').replace('.doc', '')
        elements.append(ExtractedElement(
            element_name="合同标题",
            element_category=ElementCategory.DOCUMENT_STRUCTURE,
            value=title_value,
            confidence=0.8,
            source_position={"source": "filename"},
            pattern_type="filename_extraction"
        ))

        # 文档统计信息
        elements.append(ExtractedElement(
            element_name="文档统计",
            element_category=ElementCategory.DOCUMENT_STRUCTURE,
            value=f"共{document_info.total_paragraphs}段落，{document_info.total_words}字",
            confidence=1.0,
            source_position={"source": "document_stats"},
            pattern_type="statistics"
        ))

        # 文档复杂度评估
        if document_info.total_paragraphs > 50:
            elements.append(ExtractedElement(
                element_name="合同复杂度",
                element_category=ElementCategory.DOCUMENT_STRUCTURE,
                value="中等复杂度合同",
                confidence=0.8,
                source_position={"source": "statistical_analysis"},
                pattern_type="complexity_analysis"
            ))
        elif document_info.total_paragraphs > 20:
            elements.append(ExtractedElement(
                element_name="合同复杂度",
                element_category=ElementCategory.DOCUMENT_STRUCTURE,
                value="标准复杂度合同",
                confidence=0.8,
                source_position={"source": "statistical_analysis"},
                pattern_type="complexity_analysis"
            ))

        return elements

    def _extract_filename_based_elements(self, document_info: DocumentInfo) -> List[ExtractedElement]:
        """基于文件名提取要素"""
        elements = []
        filename_lower = document_info.filename.lower()

        # 合同类型推断
        type_keywords = {
            "销售类合同": ['销售', '买卖', 'sales', '采购', '购买'],
            "服务类合同": ['服务', 'service', '咨询', '技术', '开发'],
            "租赁类合同": ['租赁', 'lease', '出租', '承租'],
            "劳动类合同": ['劳动', '雇佣', 'employment', '聘用']
        }

        for contract_type, keywords in type_keywords.items():
            if any(keyword in filename_lower for keyword in keywords):
                elements.append(ExtractedElement(
                    element_name="合同类型推断",
                    element_category=ElementCategory.CONTRACT_TERMS,
                    value=contract_type,
                    confidence=0.8,
                    source_position={"source": "filename_analysis"},
                    pattern_type="filename_analysis"
                ))
                break

        return elements

    async def _extract_by_regex_comprehensive(self, document_info: DocumentInfo, temp_file_path: str = None) -> List[ExtractedElement]:
        """
        正则全面提取 - 处理所有类型的要素
        """
        logger.info("📋 开始正则全面提取")
        elements = []

        try:
            # 基础文档信息
            elements.extend(self._extract_document_structure_elements(document_info))

            # 从文档处理器获取所有正则提取的要素
            if temp_file_path and os.path.exists(temp_file_path):
                processor_elements = await self.document_processor.extract_basic_elements(temp_file_path)
                elements.extend(processor_elements)
                logger.info(f"📊 从文档处理器获取 {len(processor_elements)} 个要素")

            # 基于文件名的推断要素
            elements.extend(self._extract_filename_based_elements(document_info))

            # 标记所有要素为正则来源
            for element in elements:
                if not element.pattern_type.startswith("regex_"):
                    element.pattern_type = f"regex_{element.pattern_type}"

            logger.info(f"✅ 正则全面提取完成: {len(elements)} 个要素")
            return elements

        except Exception as e:
            logger.error(f"正则全面提取失败: {e}")
            return elements

    async def _extract_by_ai_comprehensive(self, document_info: DocumentInfo, temp_file_path: str = None) -> List[ExtractedElement]:
        """
        AI全面提取 - 处理所有类型的要素
        """
        logger.info("🤖 开始AI全面提取")
        elements = []

        try:
            # 读取文档内容
            document_content = await self._read_document_content(temp_file_path)
            if not document_content:
                logger.warning("文档内容为空，跳过AI提取")
                return elements

            logger.info(f"📖 文档内容长度: {len(document_content)} 字符")

            # 调用千问3进行全面要素提取
            ai_result = await self.qwen_client.extract_contract_elements(
                document_content=document_content,
                contract_type="general"
            )

            if ai_result and "elements" in ai_result:
                logger.info(f"🔍 千问3返回了 {len(ai_result['elements'])} 个要素")

                for i, elem_data in enumerate(ai_result["elements"]):
                    try:
                        element = ExtractedElement(
                            element_name=elem_data.get("element_name", "未知要素"),
                            element_category=ElementCategory(elem_data.get("category", "contract_terms")),
                            value=elem_data.get("value", ""),
                            confidence=elem_data.get("confidence", 0.8),
                            source_position={"ai_extracted": True, "location": elem_data.get("source_location", "")},
                            pattern_type="ai_comprehensive_extraction"
                        )
                        elements.append(element)
                        logger.info(f"  ✅ AI要素 {i+1}: {element.element_name} = {element.value[:50]}...")
                    except Exception as e:
                        logger.warning(f"❌ 解析AI要素失败: {e}")

            logger.info(f"✅ AI全面提取完成: {len(elements)} 个要素")
            return elements

        except Exception as e:
            logger.error(f"AI全面提取失败: {e}")
            return elements

    async def _dual_verification_merge(self, results: Dict[str, List[ExtractedElement]]) -> List[ExtractedElement]:
        """
        双重验证合并结果

        策略：
        1. 寻找匹配的要素进行相互验证
        2. 对冲突结果进行智能解决
        3. 添加各自独有的要素
        4. 按置信度和验证状态排序
        """
        logger.info("🔄 开始双重验证合并")

        regex_elements = results.get("regex", [])
        ai_elements = results.get("ai", [])

        logger.info(f"📊 正则要素: {len(regex_elements)} 个")
        logger.info(f"🤖 AI要素: {len(ai_elements)} 个")

        merged_elements = []
        used_ai_indices = set()

        # 第一轮：寻找匹配项并验证
        for regex_elem in regex_elements:
            best_match = None
            best_score = 0
            best_ai_index = -1

            for i, ai_elem in enumerate(ai_elements):
                if i in used_ai_indices:
                    continue

                # 计算匹配度
                match_score = self._calculate_element_match_score(regex_elem, ai_elem)
                if match_score > best_score and match_score > 0.6:  # 降低阈值
                    best_match = ai_elem
                    best_score = match_score
                    best_ai_index = i

            if best_match:
                # 创建验证后的要素
                verified_elem = self._create_dual_verified_element(regex_elem, best_match, best_score)
                merged_elements.append(verified_elem)
                used_ai_indices.add(best_ai_index)

                logger.info(f"✅ 双重验证: {verified_elem.element_name} (匹配度: {best_score:.2f})")
            else:
                # 正则独有，创建新的要素对象以避免修改原对象
                regex_only_elem = ExtractedElement(
                    element_name=regex_elem.element_name,
                    element_category=regex_elem.element_category,
                    value=regex_elem.value,
                    confidence=regex_elem.confidence * 0.8,
                    source_position=regex_elem.source_position,
                    pattern_type=regex_elem.pattern_type,
                    verification_status="regex_only"
                )
                merged_elements.append(regex_only_elem)

                logger.info(f"📋 正则独有: {regex_elem.element_name}")

        # 第二轮：添加AI独有要素
        for i, ai_elem in enumerate(ai_elements):
            if i not in used_ai_indices:
                # AI独有，创建新的要素对象
                ai_only_elem = ExtractedElement(
                    element_name=ai_elem.element_name,
                    element_category=ai_elem.element_category,
                    value=ai_elem.value,
                    confidence=ai_elem.confidence * 0.9,
                    source_position=ai_elem.source_position,
                    pattern_type=ai_elem.pattern_type,
                    verification_status="ai_only"
                )
                merged_elements.append(ai_only_elem)

                logger.info(f"🤖 AI独有: {ai_elem.element_name}")

        # 按置信度和验证状态排序
        merged_elements.sort(key=lambda x: (
            1 if x.verification_status == "dual_verified" else 0,
            x.confidence
        ), reverse=True)

        # 统计信息
        verification_stats = {
            "total_elements": len(merged_elements),
            "dual_verified": len([e for e in merged_elements if e.verification_status == "dual_verified"]),
            "regex_only": len([e for e in merged_elements if e.verification_status == "regex_only"]),
            "ai_only": len([e for e in merged_elements if e.verification_status == "ai_only"]),
            "average_confidence": sum(e.confidence for e in merged_elements) / len(merged_elements) if merged_elements else 0
        }

        logger.info(f"✅ 双重验证合并完成: {verification_stats}")
        return merged_elements

    def _calculate_element_match_score(self, regex_elem: ExtractedElement, ai_elem: ExtractedElement) -> float:
        """计算两个要素的匹配度"""
        score = 0.0

        # 1. 要素名称相似度 (40%)
        name_similarity = self._calculate_name_similarity(regex_elem.element_name, ai_elem.element_name)
        score += name_similarity * 0.4

        # 2. 要素值相似度 (40%)
        value_similarity = self._calculate_value_similarity(regex_elem.value, ai_elem.value)
        score += value_similarity * 0.4

        # 3. 分类匹配度 (20%)
        if regex_elem.element_category == ai_elem.element_category:
            score += 0.2

        return min(score, 1.0)

    def _create_dual_verified_element(self, regex_elem: ExtractedElement, ai_elem: ExtractedElement, match_score: float) -> ExtractedElement:
        """创建经过双重验证的要素"""
        # 选择更完整的值
        final_value = ai_elem.value if len(ai_elem.value) > len(regex_elem.value) else regex_elem.value

        # 提升置信度
        boosted_confidence = min((regex_elem.confidence + ai_elem.confidence) / 2 + 0.1, 1.0)

        verified_element = ExtractedElement(
            element_name=regex_elem.element_name,  # 正则的名称通常更标准
            element_category=regex_elem.element_category,
            value=final_value,
            confidence=boosted_confidence,
            source_position=regex_elem.source_position,
            pattern_type="dual_verified"
        )

        # 添加验证详情
        verified_element.verification_status = "dual_verified"
        verified_element.verification_details = {
            "regex_confidence": regex_elem.confidence,
            "ai_confidence": ai_elem.confidence,
            "match_score": match_score,
            "regex_value": regex_elem.value,
            "ai_value": ai_elem.value,
            "verification_method": "dual_verification"
        }

        return verified_element

    async def _extract_semantic_elements(self, document_info: DocumentInfo, temp_file_path: str = None) -> List[ExtractedElement]:
        """
        提取语义要素（AI处理）

        专门处理需要语义理解的要素：
        - 权利义务条款
        - 违约责任
        - 争议解决方式
        - 特殊约定
        - 风险条款
        """
        logger.info("🤖 开始语义要素提取")
        elements = []

        try:
            # 读取文档内容
            document_content = await self._read_document_content(temp_file_path)
            if not document_content:
                logger.warning("文档内容为空，跳过AI提取")
                return elements

            logger.info(f"📖 文档内容长度: {len(document_content)} 字符")

            # 构建专门的语义提取提示
            semantic_prompt = self._build_semantic_extraction_prompt(document_content)

            # 调用千问3进行语义要素提取
            ai_result = await self.qwen_client.extract_semantic_elements(
                document_content=document_content,
                extraction_focus="semantic_terms"
            )

            if ai_result and "elements" in ai_result:
                logger.info(f"🔍 千问3返回了 {len(ai_result['elements'])} 个语义要素")

                for i, elem_data in enumerate(ai_result["elements"]):
                    try:
                        # 只处理语义类要素
                        category = elem_data.get("category", "contract_terms")
                        if category in ["contract_terms", "legal_terms", "special_clauses"]:
                            element = ExtractedElement(
                                element_name=elem_data.get("element_name", "未知语义要素"),
                                element_category=ElementCategory.CONTRACT_TERMS,
                                value=elem_data.get("value", ""),
                                confidence=elem_data.get("confidence", 0.8),
                                source_position={"ai_extracted": True, "location": elem_data.get("source_location", "")},
                                pattern_type="ai_semantic_extraction"
                            )
                            elements.append(element)
                            logger.info(f"  ✅ 语义要素 {i+1}: {element.element_name}")
                    except Exception as e:
                        logger.warning(f"❌ 解析AI语义要素失败: {e}")

            logger.info(f"✅ 语义要素提取完成: {len(elements)} 个要素")
            return elements

        except Exception as e:
            logger.error(f"语义要素提取失败: {e}")
            return elements

    def _build_semantic_extraction_prompt(self, document_content: str) -> str:
        """构建语义提取的专门提示"""
        return f"""
        请从以下合同内容中提取语义层面的要素，重点关注：

        1. 权利义务条款 - 双方的主要权利和义务
        2. 违约责任 - 违约情况和责任承担
        3. 争议解决 - 纠纷处理方式
        4. 特殊约定 - 非标准的特殊条款
        5. 履约条件 - 合同履行的前提条件
        6. 终止条款 - 合同终止的情况和程序

        合同内容（前3000字符）：
        {document_content[:3000]}

        请以JSON格式回复，只提取语义复杂的要素：
        {{
            "elements": [
                {{
                    "element_name": "要素名称",
                    "category": "contract_terms",
                    "value": "提取的具体内容",
                    "confidence": 0.85,
                    "source_location": "在文档中的位置描述"
                }}
            ]
        }}
        """

    async def _merge_extraction_results(self, results: Dict[str, List[ExtractedElement]]) -> List[ExtractedElement]:
        """
        智能合并提取结果

        策略：
        1. 结构化要素优先（正则提取的准确性更高）
        2. 语义要素补充（AI提取的理解能力更强）
        3. 去重和冲突解决
        4. 按置信度排序
        """
        logger.info("🔄 开始合并提取结果")

        structured_elements = results.get("structured", [])
        semantic_elements = results.get("semantic", [])

        logger.info(f"📊 结构化要素: {len(structured_elements)} 个")
        logger.info(f"🤖 语义要素: {len(semantic_elements)} 个")

        # 合并策略
        merged_elements = []

        # 1. 优先添加结构化要素（高置信度）
        merged_elements.extend(structured_elements)

        # 2. 添加语义要素，避免重复
        for semantic_elem in semantic_elements:
            if not self._is_duplicate_element(semantic_elem, merged_elements):
                merged_elements.append(semantic_elem)
            else:
                logger.debug(f"跳过重复语义要素: {semantic_elem.element_name}")

        # 3. 按置信度和重要性排序
        merged_elements.sort(key=lambda x: (x.confidence, self._get_element_importance(x)), reverse=True)

        # 4. 添加合并统计信息
        merge_stats = {
            "total_elements": len(merged_elements),
            "structured_count": len(structured_elements),
            "semantic_count": len([e for e in merged_elements if e.pattern_type.startswith("ai_")]),
            "average_confidence": sum(e.confidence for e in merged_elements) / len(merged_elements) if merged_elements else 0
        }

        logger.info(f"✅ 结果合并完成: {merge_stats}")
        return merged_elements

    def _is_duplicate_element(self, element: ExtractedElement, existing_elements: List[ExtractedElement]) -> bool:
        """检查要素是否重复"""
        for existing in existing_elements:
            # 检查要素名称相似度
            if self._calculate_name_similarity(element.element_name, existing.element_name) > 0.8:
                # 检查要素值相似度
                if self._calculate_value_similarity(element.value, existing.value) > 0.7:
                    return True
        return False

    def _calculate_name_similarity(self, name1: str, name2: str) -> float:
        """计算要素名称相似度"""
        if not name1 or not name2:
            return 0.0

        name1_clean = name1.lower().strip()
        name2_clean = name2.lower().strip()

        if name1_clean == name2_clean:
            return 1.0

        # 简单的包含关系检查
        if name1_clean in name2_clean or name2_clean in name1_clean:
            return 0.9

        # 关键词匹配
        keywords1 = set(name1_clean.split())
        keywords2 = set(name2_clean.split())

        if keywords1 and keywords2:
            intersection = len(keywords1 & keywords2)
            union = len(keywords1 | keywords2)
            return intersection / union if union > 0 else 0.0

        return 0.0

    def _calculate_value_similarity(self, value1: str, value2: str) -> float:
        """计算要素值相似度"""
        if not value1 or not value2:
            return 0.0

        # 简单的字符级相似度
        value1_clean = value1.lower().strip()
        value2_clean = value2.lower().strip()

        if value1_clean == value2_clean:
            return 1.0

        # 长度差异过大认为不相似
        if abs(len(value1_clean) - len(value2_clean)) > max(len(value1_clean), len(value2_clean)) * 0.5:
            return 0.0

        # 计算公共字符比例
        common_chars = sum(1 for c in value1_clean if c in value2_clean)
        max_length = max(len(value1_clean), len(value2_clean))

        return common_chars / max_length if max_length > 0 else 0.0

    def _get_element_importance(self, element: ExtractedElement) -> float:
        """获取要素重要性权重"""
        importance_weights = {
            ElementCategory.FINANCIAL_TERMS: 1.0,      # 金额最重要
            ElementCategory.PARTY_INFO: 0.9,           # 当事方信息很重要
            ElementCategory.TIME_TERMS: 0.8,           # 时间条款重要
            ElementCategory.CONTRACT_TERMS: 0.7,       # 合同条款重要
            ElementCategory.DOCUMENT_STRUCTURE: 0.5    # 文档结构相对不重要
        }

        return importance_weights.get(element.element_category, 0.6)
    
    async def _detect_missing_clauses(
        self,
        document_info: DocumentInfo,
        contract_type: ContractType
    ) -> List[MissingClause]:
        """检测缺失条款"""
        try:
            # 如果启用AI分析且有千问3客户端
            if self.qwen_client:
                # 这里可以调用千问3进行缺失条款检测
                # 暂时直接降级到规则检测
                logger.info("千问3缺失条款检测功能待实现，使用规则检测")

            # 降级到规则检测
            return self._detect_missing_by_rules(contract_type)

        except Exception as e:
            logger.warning(f"AI缺失条款检测失败，使用规则检测: {e}")
            return self._detect_missing_by_rules(contract_type)
    
    def _detect_missing_by_rules(self, contract_type: ContractType) -> List[MissingClause]:
        """基于规则的缺失条款检测"""
        required_clauses = self.required_clauses.get(contract_type, [])
        
        # 模拟检测结果 - 假设缺失部分条款
        missing_clauses = []
        for i, clause_name in enumerate(required_clauses[:2]):  # 模拟缺失前2个条款
            missing_clauses.append(MissingClause(
                clause_name=clause_name,
                clause_type="必要条款",
                importance_level=RiskLevel.MEDIUM,
                description=f"合同中缺少{clause_name}相关条款",
                recommendation=f"建议补充{clause_name}的具体内容和标准"
            ))
        
        return missing_clauses
    
    async def _identify_risk_points(
        self, 
        document_info: DocumentInfo, 
        contract_type: ContractType
    ) -> List[RiskPoint]:
        """识别风险点"""
        try:
            # 如果启用AI分析且有千问3客户端
            if self.qwen_client:
                # 这里可以调用千问3进行风险识别
                # 暂时直接降级到规则识别
                logger.info("千问3风险识别功能待实现，使用规则识别")

            # 降级到规则识别
            return self._identify_risks_by_rules(document_info, contract_type)

        except Exception as e:
            logger.warning(f"AI风险识别失败，使用规则识别: {e}")
            return self._identify_risks_by_rules(document_info, contract_type)
    
    def _identify_risks_by_rules(
        self, 
        document_info: DocumentInfo, 
        contract_type: ContractType
    ) -> List[RiskPoint]:
        """基于规则的风险识别"""
        risk_points = []
        
        # 模拟风险点识别
        if document_info.total_words < 500:
            risk_points.append(RiskPoint(
                risk_id="R001",
                risk_type="文档完整性风险",
                risk_level=RiskLevel.MEDIUM,
                description="合同内容过于简短，可能存在条款不完整的风险",
                location="整体文档",
                suggestion="建议补充详细的合同条款和约定事项",
                related_clause="整体结构"
            ))
        
        if not document_info.author:
            risk_points.append(RiskPoint(
                risk_id="R002",
                risk_type="文档来源风险",
                risk_level=RiskLevel.LOW,
                description="文档缺少作者信息，无法确认起草方",
                location="文档属性",
                suggestion="建议确认文档的起草方和版本信息",
                related_clause="文档元数据"
            ))
        
        return risk_points
    

    
    def _calculate_statistics(
        self, 
        elements: List[ExtractedElement], 
        missing_clauses: List[MissingClause], 
        risk_points: List[RiskPoint]
    ) -> Dict:
        """计算统计信息"""
        return {
            "total_elements": len(elements),
            "total_missing_clauses": len(missing_clauses),
            "total_risk_points": len(risk_points),
            "high_risk_count": len([r for r in risk_points if r.risk_level == RiskLevel.HIGH]),
            "medium_risk_count": len([r for r in risk_points if r.risk_level == RiskLevel.MEDIUM]),
            "low_risk_count": len([r for r in risk_points if r.risk_level == RiskLevel.LOW])
        }
    
    def _calculate_overall_confidence(self, elements: List[ExtractedElement]) -> float:
        """计算整体置信度"""
        if not elements:
            return 0.5  # 基础置信度

        # 计算加权平均置信度
        total_confidence = sum(elem.confidence for elem in elements)
        avg_confidence = total_confidence / len(elements)

        # 基于要素数量调整置信度
        element_bonus = min(len(elements) * 0.1, 0.3)  # 最多30%的奖励

        return min(avg_confidence + element_bonus, 1.0)
    
    def _calculate_completeness_score(
        self, 
        missing_clauses: List[MissingClause], 
        contract_type: ContractType
    ) -> float:
        """计算完整性评分"""
        required_count = len(self.required_clauses.get(contract_type, []))
        if required_count == 0:
            return 1.0
        
        missing_count = len(missing_clauses)
        return max(0.0, (required_count - missing_count) / required_count)
    
    def _calculate_risk_score(self, risk_points: List[RiskPoint]) -> float:
        """计算风险评分"""
        if not risk_points:
            return 0.0
        
        risk_weights = {RiskLevel.HIGH: 1.0, RiskLevel.MEDIUM: 0.6, RiskLevel.LOW: 0.3}
        total_risk = sum(risk_weights.get(risk.risk_level, 0.5) for risk in risk_points)
        
        # 归一化到0-1范围
        return min(total_risk / len(risk_points), 1.0)
    
    def _group_elements_by_category(
        self, 
        elements: List[ExtractedElement]
    ) -> Dict[str, List[ExtractedElement]]:
        """按分类组织要素"""
        grouped = {}
        for element in elements:
            category = element.element_category.value
            if category not in grouped:
                grouped[category] = []
            grouped[category].append(element)
        
        return grouped

    async def _read_document_content(self, file_path: str) -> str:
        """读取文档内容用于AI分析"""
        try:
            from docx import Document

            doc = Document(file_path)
            content_parts = []

            # 提取段落内容
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    content_parts.append(paragraph.text.strip())

            # 提取表格内容
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text.strip())
                    if row_text:
                        content_parts.append(" | ".join(row_text))

            full_content = "\n".join(content_parts)

            # 限制内容长度，避免超出API限制
            max_length = self.config.get("max_content_length", 10000)
            if len(full_content) > max_length:
                full_content = full_content[:max_length] + "\n...(内容已截断)"

            return full_content

        except Exception as e:
            logger.error(f"读取文档内容失败: {e}")
            return ""
