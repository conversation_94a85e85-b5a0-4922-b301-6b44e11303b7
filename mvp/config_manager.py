"""
配置管理模块
管理系统配置、API密钥和环境变量
"""

import os
import logging
from typing import Dict, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置"""
        config = {
            # 服务器配置
            "server": {
                "host": os.getenv("HOST", "0.0.0.0"),
                "port": int(os.getenv("PORT", "8000")),
                "debug": os.getenv("DEBUG", "true").lower() == "true",
                "reload": os.getenv("RELOAD", "true").lower() == "true"
            },
            
            # 文件处理配置
            "file_processing": {
                "max_file_size": int(os.getenv("MAX_FILE_SIZE", str(50 * 1024 * 1024))),  # 50MB
                "supported_formats": [".doc", ".docx"],
                "temp_dir": os.getenv("TEMP_DIR", "./temp"),
                "upload_timeout": int(os.getenv("UPLOAD_TIMEOUT", "300"))  # 5分钟
            },
            
            # 千问3 API配置
            "qwen_api": {
                "api_key": os.getenv("QWEN_API_KEY", ""),
                "base_url": os.getenv("QWEN_BASE_URL", "https://dashscope.aliyuncs.com/compatible-mode/v1"),
                "model_name": os.getenv("QWEN_MODEL", "qwen-plus"),
                "timeout": int(os.getenv("QWEN_TIMEOUT", "60")),
                "max_retries": int(os.getenv("QWEN_MAX_RETRIES", "3")),
                "rate_limit_delay": float(os.getenv("QWEN_RATE_LIMIT_DELAY", "1.0"))
            },
            
            # 分析配置
            "analysis": {
                "enable_ai_analysis": os.getenv("ENABLE_AI_ANALYSIS", "true").lower() == "true",
                "fallback_to_rules": os.getenv("FALLBACK_TO_RULES", "true").lower() == "true",
                "confidence_threshold": float(os.getenv("CONFIDENCE_THRESHOLD", "0.7")),
                "max_content_length": int(os.getenv("MAX_CONTENT_LENGTH", "10000"))  # 发送给AI的最大内容长度
            },
            
            # 日志配置
            "logging": {
                "level": os.getenv("LOG_LEVEL", "INFO"),
                "log_dir": os.getenv("LOG_DIR", "./logs"),
                "max_file_size": int(os.getenv("LOG_MAX_FILE_SIZE", str(10 * 1024 * 1024))),  # 10MB
                "backup_count": int(os.getenv("LOG_BACKUP_COUNT", "5"))
            },
            
            # 安全配置
            "security": {
                "cors_origins": os.getenv("CORS_ORIGINS", "*").split(","),
                "max_requests_per_minute": int(os.getenv("MAX_REQUESTS_PER_MINUTE", "60")),
                "enable_api_key_auth": os.getenv("ENABLE_API_KEY_AUTH", "false").lower() == "true",
                "api_key": os.getenv("API_KEY", "")
            }
        }
        
        # 验证关键配置
        self._validate_config(config)
        
        return config
    
    def _validate_config(self, config: Dict[str, Any]) -> None:
        """验证配置的有效性"""
        # 检查千问3 API密钥
        if config["analysis"]["enable_ai_analysis"]:
            if not config["qwen_api"]["api_key"]:
                logger.warning("千问3 API密钥未配置，将使用规则匹配模式")
                config["analysis"]["enable_ai_analysis"] = False
        
        # 检查目录
        temp_dir = Path(config["file_processing"]["temp_dir"])
        temp_dir.mkdir(exist_ok=True)
        
        log_dir = Path(config["logging"]["log_dir"])
        log_dir.mkdir(exist_ok=True)
        
        logger.info("配置验证完成")
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key_path: 配置路径，如 "qwen_api.api_key"
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key_path.split(".")
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key_path: str, value: Any) -> None:
        """
        设置配置值
        
        Args:
            key_path: 配置路径
            value: 配置值
        """
        keys = key_path.split(".")
        config = self.config
        
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        config[keys[-1]] = value
    
    def is_ai_enabled(self) -> bool:
        """检查AI分析是否启用"""
        return (
            self.get("analysis.enable_ai_analysis", False) and 
            bool(self.get("qwen_api.api_key", ""))
        )
    
    def get_qwen_config(self) -> Dict[str, Any]:
        """获取千问3配置"""
        return self.get("qwen_api", {})
    
    def get_file_config(self) -> Dict[str, Any]:
        """获取文件处理配置"""
        return self.get("file_processing", {})
    
    def get_analysis_config(self) -> Dict[str, Any]:
        """获取分析配置"""
        return self.get("analysis", {})
    
    def print_config_status(self) -> None:
        """打印配置状态"""
        logger.info("=== 系统配置状态 ===")
        logger.info(f"AI分析: {'启用' if self.is_ai_enabled() else '禁用'}")
        logger.info(f"千问3 API: {'已配置' if self.get('qwen_api.api_key') else '未配置'}")
        logger.info(f"规则降级: {'启用' if self.get('analysis.fallback_to_rules') else '禁用'}")
        logger.info(f"最大文件大小: {self.get('file_processing.max_file_size') // (1024*1024)}MB")
        logger.info(f"临时目录: {self.get('file_processing.temp_dir')}")
        logger.info("==================")

# 全局配置实例
config_manager = ConfigManager()
